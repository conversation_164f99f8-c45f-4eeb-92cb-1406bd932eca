// The order of imports matters
import 'react-native-url-polyfill/auto';
import './shim';

import {AppRegistry, Text, TextInput} from 'react-native';

import Root from '@/Root';
import {name as appName} from './app.json';

// Disable font scaling globally
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;

AppRegistry.registerComponent(appName, () => Root);
