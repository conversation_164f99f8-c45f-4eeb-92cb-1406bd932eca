import {
  AUTHENTICATION_SERVICE,
  EMAIL_LOGGER_SERVICE,
  NOTIFICATION_SERVICE,
  PRICE_FETCHING_SERVICE,
  STAKING_CALCULATOR_BACKEND_SERVICE,
  SWAPS_SERVICE,
  WALLET_BALANCE_SERVICE,
  WALLET_LOGGER_SERVICE,
  STATUS_SERVICE,
  RAMPS_SERVICE,
} from '@env';
import axios from 'axios';

const WalletLoggerInstance = axios.create({
  baseURL: WALLET_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StakingCalculatorInstance = axios.create({
  baseURL: STAKING_CALCULATOR_BACKEND_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const WalletBalanceInstance = axios.create({
  baseURL: WALLET_BALANCE_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const PriceFetchingInstance = axios.create({
  baseURL: PRICE_FETCHING_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const EmailLoggerInstance = axios.create({
  baseURL: EMAIL_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const NotificationInstance = axios.create({
  baseURL: NOTIFICATION_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const AuthenticationInstance = axios.create({
  baseURL: AUTHENTICATION_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const RampsInstance = axios.create({
  baseURL: RAMPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const ExchangeInstance = axios.create({
  baseURL: SWAPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StatusInstance = axios.create({
  baseURL: STATUS_SERVICE,
  // headers: {
  //   'content-type': 'application/json',
  // },
});

export {
  WalletLoggerInstance,
  StakingCalculatorInstance,
  WalletBalanceInstance,
  PriceFetchingInstance,
  EmailLoggerInstance,
  NotificationInstance,
  AuthenticationInstance,
  ExchangeInstance,
  RampsInstance,
  StatusInstance,
};
