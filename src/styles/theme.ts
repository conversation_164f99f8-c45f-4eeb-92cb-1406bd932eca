import {Dimensions, PixelRatio, useColorScheme} from 'react-native';

export const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

const scale = SCREEN_WIDTH / 375;
const scaleHeight = SCREEN_HEIGHT / 812;

/* ============================================================================================== */
/*                                              HOOKS                                             */
/* ============================================================================================== */

export const useThemeColors = () => {
  const colorScheme = useColorScheme();
  return colorScheme === 'dark' ? colors.dark : colors.light;
};

/* ============================================================================================== */
/*                                              UTILS                                             */
/* ============================================================================================== */

export const normalize = (size: number, forHeight?: boolean) => {
  const newSize = size * (forHeight ? scaleHeight : scale);
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

export const normalizeFont = (size: number) => {
  const scaledSize = size * scale * PixelRatio.getFontScale();
  return clamp(Math.round(PixelRatio.roundToNearestPixel(scaledSize)), 10, 40);
};

/**
 * Clamps a value between min and max
 */
const clamp = (value: number, min: number, max: number) =>
  Math.min(Math.max(value, min), max);

/* ============================================================================================== */
/*                                            CONSTANTS                                           */
/* ============================================================================================== */

const isSmallDevice = SCREEN_WIDTH < 375 || SCREEN_HEIGHT < 670;

export const colors = {
  brand: {
    main: '#633c61',
    mainLight: '#7a4d74',
    coral: '#eb634b',
    coralLight: '#ee5339',
  },
  light: {
    background: '#FFFFFF',
    text: '#000000',
    primary: '#007AFF',
    secondary: '#34C759',
    border: '#E5E5E5',
  },
  dark: {
    background: '#000000',
    text: '#FFFFFF',
    primary: '#0A84FF',
    secondary: '#30D158',
    border: '#3A3A3A',
  },
  base: {
    white: '#FFFFFF',
    black: '#000000',
  },
  neutral: {
    500: '#656566',
  },
};

export const fonts = {
  default: 'SF-Pro',
};

export const typography = {
  xs: normalizeFont(12),
  sm: normalizeFont(16),
  md: normalizeFont(22),
  lg: normalizeFont(26),
  xl: normalizeFont(30),
  xxl: normalizeFont(34),
};

export const spacing = {
  sm: normalize(8),
  md: normalize(12),
  lg: normalize(16),
  xl: normalize(24),
  xxl: normalize(32),
  xxxl: normalize(40),
} as const;

export const layout = {
  images: {
    xs: normalize(40),
    sm: normalize(80),
    md: normalize(150),
    lg: normalize(200),
  },
  ph: {
    sm: normalize(16),
    md: normalize(32),
    lg: normalize(48),
    screen: isSmallDevice ? spacing.md : spacing.lg,
  },
  pv: {
    sm: normalize(12),
    md: normalize(24),
    lg: normalize(36),
    screen: isSmallDevice ? spacing.sm : spacing.sm,
  },
  gap: {
    screen: spacing.lg,
  },
  borderRadius: {
    sm: normalize(8),
    md: normalize(14),
    lg: normalize(20),
  },
} as const;

export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  isSmallDevice,
  colors,
  fonts,
  typography,
  spacing,
  layout,
};
