import React, {memo, useCallback, useMemo, useState} from 'react';
import {
  FlatList,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import SearchIcon from '@/assets/icons/magnifying glass.svg';
import CurrencyIcon from '@/components/CurrencyIcon';
import GlobalStyles from '@/constants/GlobalStyles';
import {capitalizeAll} from '@/utils';
import BottomSheetWrapper from './BottomSheetWrapper';
import SafeAreaInset from './SafeAreaInset';

export type BaseOption = {
  label: string;
  value: string;
};

export type CurrencyOption = BaseOption & {
  fullName: string;
  type: 'fiat' | 'crypto';
};

export type BottomSheetListType =
  | 'currency'
  | 'simple'
  | 'lightningSend'
  | 'lightningHistory';

export type BottomSheetListData = {
  type: BottomSheetListType;
  data: (BaseOption | CurrencyOption)[];
  onSelect?: (item: BaseOption | CurrencyOption) => void;
};

type BottomSheetListProps = {
  isOpen: boolean;
  data: (BaseOption | CurrencyOption)[];
  onSelect?: (item: BaseOption | CurrencyOption) => void;
  onClose: () => void;
  snapPoints: number[];
  enableSearch?: boolean;
};

export const BottomSheetList = ({
  isOpen,
  snapPoints,
  data,
  onSelect,
  onClose,
  enableSearch = false,
}: BottomSheetListProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  const isCurrencyOption = (
    item: CurrencyOption | BaseOption,
  ): item is CurrencyOption => {
    return 'fullName' in item && 'type' in item;
  };

  const filteredData = useMemo(() => {
    if (!enableSearch || !searchQuery) return data;

    const query = searchQuery.toLowerCase();
    return data.filter((item) => {
      if (isCurrencyOption(item)) {
        return (
          item.label.toLowerCase().includes(query) ||
          item.fullName.toLowerCase().includes(query)
        );
      }
      return item.label.toLowerCase().includes(query);
    });
  }, [data, searchQuery, enableSearch]);

  const handleSelect = (item: CurrencyOption | BaseOption) => {
    onSelect?.(item);
    onClose();
  };

  const renderItem = useCallback(
    ({item}: {item: CurrencyOption | BaseOption}) => (
      <TouchableOpacity
        style={[styles.optionButton]}
        onPress={() => handleSelect(item)}
        activeOpacity={0.7}
      >
        {isCurrencyOption(item) ? (
          <View style={styles.optionContainer}>
            <View style={styles.optionIconContainer}>
              <CurrencyIcon currency={item} />
            </View>
            <View style={styles.optionTextContainer}>
              <Text style={styles.optionFullName}>{item.fullName}</Text>
              <Text style={styles.optionAbbreviation}>{capitalizeAll(item.label)}</Text>
            </View>
          </View>
        ) : (
          <Text style={styles.optionText}>{item.label}</Text>
        )}
      </TouchableOpacity>
    ),
    [onSelect, onClose],
  );

  return (
    <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPoints} onClose={onClose}>
      <View style={styles.container}>
        {enableSearch && (
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <SearchIcon width={20} height={20} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search"
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
          </View>
        )}

        <FlatList
          data={filteredData}
          keyExtractor={(item) => `${item.value}-${item.label}`}
          renderItem={renderItem}
          showsVerticalScrollIndicator={true}
          contentContainerStyle={styles.listContent}
        />

        <SafeAreaInset type="bottom" />
      </View>
    </BottomSheetWrapper>
  );
};

export default memo(BottomSheetList);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray300,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 50,
  },
  searchIcon: {
    marginRight: 8,
    color: GlobalStyles.gray.gray600,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  optionButton: {
    padding: 18,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  optionIconContainer: {
    marginRight: 15,
    width: 30,
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
  },
  optionFullName: {
    fontSize: 14,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
  },
  optionAbbreviation: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
    marginTop: 2,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  listContent: {
    padding: 4,
  },
});
