import ADA from '@/assets/assets/asset=ADA.svg';
import AKT from '@/assets/assets/asset=AKT.svg';
import APT from '@/assets/assets/asset=APT.svg';
import ATOM from '@/assets/assets/asset=ATOM.svg';
import AVAX from '@/assets/assets/asset=AVAX.svg';
import AZERO from '@/assets/assets/asset=AZERO.svg';
import BAND from '@/assets/assets/asset=BAND.svg';
import BCH from '@/assets/assets/asset=BCH.svg';
import BTC from '@/assets/assets/asset=BITCOIN.svg';
import BNB from '@/assets/assets/asset=BNB.svg';
import BSC from '@/assets/assets/asset=BSC.svg';
import CANTO from '@/assets/assets/asset=CANTO.svg';
import CELO from '@/assets/assets/asset=CELO.svg';
import CHEQ from '@/assets/assets/asset=CHEQ.svg';
import CMDX from '@/assets/assets/asset=CMDX.svg';
import CRO from '@/assets/assets/asset=CRO.svg';
import CSPR from '@/assets/assets/asset=CSPR.svg';
import DASH from '@/assets/assets/asset=DASH.svg';
import DOGE from '@/assets/assets/asset=DOGE.svg';
import DOT from '@/assets/assets/asset=DOT.svg';
import DSM from '@/assets/assets/asset=DSM.svg';
import DVPN from '@/assets/assets/asset=DVPN.svg';
import ETHClassic from '@/assets/assets/asset=ETH-Classic.svg';
import ETH from '@/assets/assets/asset=ETHEREUM.svg';
import EVMOS from '@/assets/assets/asset=EVMOS.svg';
import FIS from '@/assets/assets/asset=FIS.svg';
import FLOW from '@/assets/assets/asset=FLOW.svg';
import FTM from '@/assets/assets/asset=FTM.svg';
import GNO from '@/assets/assets/asset=GNO.svg';
import GRT from '@/assets/assets/asset=GRT.svg';
import HBAR from '@/assets/assets/asset=HBAR.svg';
import INJ from '@/assets/assets/asset=INJ.svg';
import IRIS from '@/assets/assets/asset=IRIS.svg';
import JUNO from '@/assets/assets/asset=JUNO.svg';
import KAVA from '@/assets/assets/asset=KAVA.svg';
import KLV from '@/assets/assets/asset=KLV.svg';
import KNC from '@/assets/assets/asset=KNC.svg';
import KSM from '@/assets/assets/asset=KSM.svg';
import KAS from '@/assets/assets/asset=KAS.svg';
import LTC from '@/assets/assets/asset=LTC.svg';
import LUNC from '@/assets/assets/asset=LUNC.svg';
import MATIC from '@/assets/assets/asset=MATIC.svg';
import MINA from '@/assets/assets/asset=MINA.svg';
import MNTL from '@/assets/assets/asset=MNTL.svg';
import NEAR from '@/assets/assets/asset=NEAR.svg';
import NGM from '@/assets/assets/asset=NGM.svg';
import NRG from '@/assets/assets/asset=NRG.svg';
import ONE from '@/assets/assets/asset=ONE.svg';
import OSMO from '@/assets/assets/asset=OSMO.svg';
import REGEN from '@/assets/assets/asset=REGEN.svg';
import ROWAN from '@/assets/assets/asset=ROWAN.svg';
import RPL from '@/assets/assets/asset=RPL.svg';
import SCRT from '@/assets/assets/asset=SCRT.svg';
import SEI from '@/assets/assets/asset=SEI.svg';
import SKL from '@/assets/assets/asset=SKL.svg';
import SOL from '@/assets/assets/asset=SOL.svg';
import STARS from '@/assets/assets/asset=STARS.svg';
import SUI from '@/assets/assets/asset=SUI.svg';
import TETHER from '@/assets/assets/asset=TETHER.svg';
import TRX from '@/assets/assets/asset=TRX.svg';
import UMEE from '@/assets/assets/asset=UMEE.svg';
import XPRT from '@/assets/assets/asset=XPRT.svg';
import XRD from '@/assets/assets/asset=XRD.svg';
import XRP from '@/assets/assets/asset=XRP.svg';
import XTZ from '@/assets/assets/asset=XTZ.svg';
import ZIL from '@/assets/assets/asset=ZIL.svg';
import EGLD from '@/assets/assets/asset=eGLD.svg';

import USDTETH from '@/assets/assets/stable-ETH/asset=USDT.svg';
import USDCETH from '@/assets/assets/stable-ETH/asset=USDC.svg';
import TUSDETH from '@/assets/assets/stable-ETH/asset=TUSD.svg';
import DAIETH from '@/assets/assets/stable-ETH/asset=DAI.svg';

import USDTBNB from '@/assets/assets/stable-BSC/asset=USDT.svg';
import USDCBNB from '@/assets/assets/stable-BSC/asset=USDC.svg';
import TUSDBNB from '@/assets/assets/stable-BSC/asset=TUSD.svg';
import DAIBNB from '@/assets/assets/stable-BSC/asset=DAI.svg';
import USDTTRX from '@/assets/assets/stable-TRX/asset=USDT.svg';
import USDCTRX from '@/assets/assets/stable-TRX/asset=USDC.svg';
import TUSDTRX from '@/assets/assets/stable-TRX/asset=TUSD.svg';
import USDTAVAX from '@/assets/assets/stable-AVAX/asset=USDT.svg';
import USDCAVAX from '@/assets/assets/stable-AVAX/asset=USDC.svg';
import DAIAVAX from '@/assets/assets/stable-AVAX/asset=DAI.svg';
import TUSDAVAX from '@/assets/assets/stable-AVAX/asset=TUSD.svg';

import LIGHTNING from '@/assets/logo/lightning-network.svg';

const ADASVG: React.FC = () => <ADA height={'150%'} style={{alignSelf: 'center'}} />;
const AKTSVG: React.FC = () => <AKT height={'150%'} style={{alignSelf: 'center'}} />;
const APTSVG: React.FC = () => <APT height={'150%'} style={{alignSelf: 'center'}} />;
const ATOMSVG: React.FC = () => <ATOM height={'150%'} style={{alignSelf: 'center'}} />;
const AVAXSVG: React.FC = () => <AVAX height={'150%'} style={{alignSelf: 'center'}} />;
const AZEROSVG: React.FC = () => <AZERO height={'150%'} style={{alignSelf: 'center'}} />;
const BANDSVG: React.FC = () => <BAND height={'150%'} style={{alignSelf: 'center'}} />;
const BCHSVG: React.FC = () => <BCH height={'150%'} style={{alignSelf: 'center'}} />;
const BTCSVG: React.FC = () => <BTC height={'150%'} style={{alignSelf: 'center'}} />;
const BNBSVG: React.FC = () => <BNB height={'150%'} style={{alignSelf: 'center'}} />;
const BSCSVG: React.FC = () => <BSC height={'150%'} style={{alignSelf: 'center'}} />;
const CANTOSVG: React.FC = () => <CANTO height={'150%'} style={{alignSelf: 'center'}} />;
const CELOSVG: React.FC = () => <CELO height={'150%'} style={{alignSelf: 'center'}} />;
const CHEQSVG: React.FC = () => <CHEQ height={'150%'} style={{alignSelf: 'center'}} />;
const CMDXSVG: React.FC = () => <CMDX height={'150%'} style={{alignSelf: 'center'}} />;
const CROSVG: React.FC = () => <CRO height={'150%'} style={{alignSelf: 'center'}} />;
const CSPRSVG: React.FC = () => <CSPR height={'150%'} style={{alignSelf: 'center'}} />;
const DASHSVG: React.FC = () => <DASH height={'150%'} style={{alignSelf: 'center'}} />;
const DOGESVG: React.FC = () => <DOGE height={'150%'} style={{alignSelf: 'center'}} />;
const DOTSVG: React.FC = () => <DOT height={'150%'} style={{alignSelf: 'center'}} />;
const DSMSVG: React.FC = () => <DSM height={'150%'} style={{alignSelf: 'center'}} />;
const DVPNSVG: React.FC = () => <DVPN height={'150%'} style={{alignSelf: 'center'}} />;
const ETHClassicSVG: React.FC = () => (
  <ETHClassic height={'150%'} style={{alignSelf: 'center'}} />
);
const ETHSVG: React.FC = () => <ETH height={'150%'} style={{alignSelf: 'center'}} />;
const EVMOSSVG: React.FC = () => <EVMOS height={'150%'} style={{alignSelf: 'center'}} />;
const FISSVG: React.FC = () => <FIS height={'150%'} style={{alignSelf: 'center'}} />;
const FLOWSVG: React.FC = () => <FLOW height={'150%'} style={{alignSelf: 'center'}} />;
const FTMSVG: React.FC = () => <FTM height={'150%'} style={{alignSelf: 'center'}} />;
const GNOSVG: React.FC = () => <GNO height={'150%'} style={{alignSelf: 'center'}} />;
const GRTSVG: React.FC = () => <GRT height={'150%'} style={{alignSelf: 'center'}} />;
const HBARSVG: React.FC = () => <HBAR height={'150%'} style={{alignSelf: 'center'}} />;
const INJSVG: React.FC = () => <INJ height={'150%'} style={{alignSelf: 'center'}} />;
const IRISSVG: React.FC = () => <IRIS height={'150%'} style={{alignSelf: 'center'}} />;
const JUNOSVG: React.FC = () => <JUNO height={'150%'} style={{alignSelf: 'center'}} />;
const KAVASVG: React.FC = () => <KAVA height={'150%'} style={{alignSelf: 'center'}} />;
const KLVSVG: React.FC = () => <KLV height={'150%'} style={{alignSelf: 'center'}} />;
const KNCSVG: React.FC = () => <KNC height={'150%'} style={{alignSelf: 'center'}} />;
const KSMSVG: React.FC = () => <KSM height={'150%'} style={{alignSelf: 'center'}} />;
const KASSVG: React.FC = () => <KAS height={'150%'} style={{alignSelf: 'center'}} />;
const LTCSVG: React.FC = () => <LTC height={'150%'} style={{alignSelf: 'center'}} />;
const LUNCSVG: React.FC = () => <LUNC height={'150%'} style={{alignSelf: 'center'}} />;
const MATICSVG: React.FC = () => <MATIC height={'150%'} style={{alignSelf: 'center'}} />;
const MINASVG: React.FC = () => <MINA height={'150%'} style={{alignSelf: 'center'}} />;
const MNTLSVG: React.FC = () => <MNTL height={'150%'} style={{alignSelf: 'center'}} />;
const NEARSVG: React.FC = () => <NEAR height={'150%'} style={{alignSelf: 'center'}} />;
const NGMSVG: React.FC = () => <NGM height={'150%'} style={{alignSelf: 'center'}} />;
const NRGSVG: React.FC = () => <NRG height={'150%'} style={{alignSelf: 'center'}} />;
const ONESVG: React.FC = () => <ONE height={'150%'} style={{alignSelf: 'center'}} />;
const OSMOSVG: React.FC = () => <OSMO height={'150%'} style={{alignSelf: 'center'}} />;
const REGENSVG: React.FC = () => <REGEN height={'150%'} style={{alignSelf: 'center'}} />;
const ROWANSVG: React.FC = () => <ROWAN height={'150%'} style={{alignSelf: 'center'}} />;
const RPLSVG: React.FC = () => <RPL height={'150%'} style={{alignSelf: 'center'}} />;
const SCRTSVG: React.FC = () => <SCRT height={'150%'} style={{alignSelf: 'center'}} />;
const SEISVG: React.FC = () => <SEI height={'150%'} style={{alignSelf: 'center'}} />;
const SKLSVG: React.FC = () => <SKL height={'150%'} style={{alignSelf: 'center'}} />;
const SOLSVG: React.FC = () => <SOL height={'100%'} style={{alignSelf: 'center'}} />;
const STARSSVG: React.FC = () => <STARS height={'150%'} style={{alignSelf: 'center'}} />;
const SUISVG: React.FC = () => <SUI height={'150%'} style={{alignSelf: 'center'}} />;
const TETHERSVG: React.FC = () => (
  <TETHER height={'150%'} style={{alignSelf: 'center'}} />
);
const TRXSVG: React.FC = () => <TRX height={'150%'} style={{alignSelf: 'center'}} />;
const UMEESVG: React.FC = () => <UMEE height={'150%'} style={{alignSelf: 'center'}} />;
const XPRTSVG: React.FC = () => <XPRT height={'150%'} style={{alignSelf: 'center'}} />;
const XRDSVG: React.FC = () => <XRD height={'150%'} style={{alignSelf: 'center'}} />;
const XRPSVG: React.FC = () => <XRP height={'150%'} style={{alignSelf: 'center'}} />;
const XTZSVG: React.FC = () => <XTZ height={'150%'} style={{alignSelf: 'center'}} />;
const ZILSVG: React.FC = () => <ZIL height={'150%'} style={{alignSelf: 'center'}} />;
const EGLDSVG: React.FC = () => <EGLD height={'150%'} style={{alignSelf: 'center'}} />;
// Solana
import USDTSOL from '@/assets/assets/stable-SOL/asset=USDT.svg';
import USDCSOL from '@/assets/assets/stable-SOL/asset=USDC.svg';
import USDSSOL from '@/assets/assets/stable-SOL/asset=USDS.svg';
import JUPSOL from '@/assets/assets/stable-SOL/asset=JUP.svg';
import RAYSOL from '@/assets/assets/stable-SOL/asset=RAY.svg';

const USDTETHSVG: React.FC = () => (
  <USDTETH
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDCETHSVG: React.FC = () => (
  <USDCETH
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const TUSDETHSVG: React.FC = () => (
  <TUSDETH
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const DAIETHSVG: React.FC = () => (
  <DAIETH height={'150%'} style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}} />
);
const USDTBNBSVG: React.FC = () => (
  <USDTBNB
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDCBNBSVG: React.FC = () => (
  <USDCBNB
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const TUSDBNBSVG: React.FC = () => (
  <TUSDBNB
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const DAIBNBSVG: React.FC = () => (
  <DAIBNB height={'150%'} style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}} />
);

const USDTTRXSVG: React.FC = () => (
  <USDTTRX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDCTRXSVG: React.FC = () => (
  <USDCTRX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const TUSDTRXSVG: React.FC = () => (
  <TUSDTRX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDTAVAXSVG: React.FC = () => (
  <USDTAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDCAVAXSVG: React.FC = () => (
  <USDCAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const DAIAVAXSVG: React.FC = () => (
  <DAIAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const TUSDAVAXSVG: React.FC = () => (
  <TUSDAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDTEAVAXSVG: React.FC = () => (
  <USDTAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const USDCEAVAXSVG: React.FC = () => (
  <USDCAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);
const DAIEAVAXSVG: React.FC = () => (
  <DAIAVAX
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);

const LIGHTNINGSVG: React.FC = () => (
  <LIGHTNING height={'60%'} style={{alignSelf: 'center'}} />
);

const USDTSOLSVG: React.FC = () => (
  <USDTSOL
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);

const USDCSOLSVG: React.FC = () => (
  <USDCSOL
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);

const USDSSOLSVG: React.FC = () => (
  <USDSSOL
    height={'150%'}
    style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}}
  />
);

const JUPSOLSVG: React.FC = () => (
  <JUPSOL height={'150%'} style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}} />
);

const RAYSOLSVG: React.FC = () => (
  <RAYSOL height={'150%'} style={{alignSelf: 'center', marginLeft: 4, marginBottom: 4}} />
);

type LogoProps = {
  name: string;
};

const Logo: React.FC<LogoProps> = ({name}) => {
  const componentNames: {[key: string]: React.FC} = {
    ADASVG,
    AKTSVG,
    APTSVG,
    ATOMSVG,
    AVAXSVG,
    AZEROSVG,
    BANDSVG,
    BCHSVG,
    BTCSVG,
    BNBSVG,
    BSCSVG,
    CANTOSVG,
    CELOSVG,
    CHEQSVG,
    CMDXSVG,
    CROSVG,
    CSPRSVG,
    DASHSVG,
    DOGESVG,
    DOTSVG,
    DSMSVG,
    DVPNSVG,
    ETHClassicSVG,
    ETHSVG,
    EVMOSSVG,
    FISSVG,
    FLOWSVG,
    FTMSVG,
    GNOSVG,
    GRTSVG,
    HBARSVG,
    INJSVG,
    IRISSVG,
    JUNOSVG,
    KAVASVG,
    KLVSVG,
    KNCSVG,
    KSMSVG,
    KASSVG,
    LTCSVG,
    LUNCSVG,
    MATICSVG,
    MINASVG,
    MNTLSVG,
    NEARSVG,
    NGMSVG,
    NRGSVG,
    ONESVG,
    OSMOSVG,
    REGENSVG,
    ROWANSVG,
    RPLSVG,
    SCRTSVG,
    SEISVG,
    SKLSVG,
    SOLSVG,
    STARSSVG,
    SUISVG,
    TETHERSVG,
    TRXSVG,
    UMEESVG,
    XPRTSVG,
    XRDSVG,
    XRPSVG,
    XTZSVG,
    ZILSVG,
    EGLDSVG,
    USDTETHSVG,
    USDCETHSVG,
    TUSDETHSVG,
    DAIETHSVG,

    USDTBNBSVG,
    USDCBNBSVG,
    TUSDBNBSVG,
    DAIBNBSVG,

    // USDTBSCSVG,
    // USDCBSCSVG,
    // TUSDBSCSVG,
    // DAIBSCSVG,
    USDTTRXSVG,
    USDCTRXSVG,
    TUSDTRXSVG,
    USDTAVAXSVG,
    USDCAVAXSVG,
    DAIAVAXSVG,
    TUSDAVAXSVG,
    USDTEAVAXSVG,
    USDCEAVAXSVG,
    DAIEAVAXSVG,

    LIGHTNINGSVG,
    USDTSOLSVG,
    USDCSOLSVG,
    USDSSOLSVG,
    JUPSOLSVG,
    RAYSOLSVG,
  };

  const Component = componentNames[`${name}`] || componentNames['ETHSVG'];
  if (componentNames[`${name}`] == undefined) {
    console.log(`Logo component with name ${name} does not exist`);
  }

  return <Component />;
};

export default Logo;
