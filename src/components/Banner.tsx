import React from 'react';
import {StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {BodySB} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {
  AcademicCapIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
} from 'react-native-heroicons/outline';

export type Props = {
  type: 'info' | 'warning' | 'highSeverity';
  message: string;
};

const Banner: React.FC<Props> = ({type, message}) => {
  const getBannerStyles = () => {
    switch (type) {
      case 'info':
        return {
          backgroundColor: GlobalStyles.gray.gray300,
          icon: (
            <AcademicCapIcon
              width={32}
              height={32}
              stroke={GlobalStyles.success.success900}
            />
          ),
        };
      case 'warning':
        return {
          backgroundColor: GlobalStyles.gray.gray300,
          icon: (
            <ExclamationTriangleIcon
              width={32}
              height={32}
              stroke={GlobalStyles.orange.orange600}
            />
          ),
        };
      case 'highSeverity':
        return {
          backgroundColor: GlobalStyles.gray.gray300,
          icon: (
            <ExclamationCircleIcon
              width={32}
              height={32}
              stroke={GlobalStyles.error.error900}
            />
          ),
        };
    }
  };

  const {backgroundColor, icon} = getBannerStyles();

  return (
    <View style={[styles.bannerContent, {backgroundColor}]}>
      <View style={styles.iconContainer}>{icon}</View>

      <View style={styles.messageContainer}>
        <BodySB style={styles.message}>{message}</BodySB>
      </View>
    </View>
  );
};

export default Banner;

const styles = StyleSheet.create({
  bannerContent: {
    width: '100%',
    flexDirection: 'row',
    borderColor: GlobalStyles.gray.gray500,
    borderWidth: 1,
    borderRadius: 12,
    alignItems: 'center',
    paddingVertical: theme.layout.pv.sm,
    paddingHorizontal: theme.layout.ph.sm,
    // Shadows
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    paddingTop: 2,
    marginRight: 16,
  },
  messageContainer: {
    flex: 1,
    flexShrink: 1,
  },
  message: {
    flexWrap: 'wrap',
  },
});
