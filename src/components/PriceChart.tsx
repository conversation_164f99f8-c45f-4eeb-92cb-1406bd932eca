// import {
//   Canvas,
//   Circle,
//   CornerPathEffect,
//   Group,
//   Line,
//   LinearGradient,
//   Mask,
//   Path,
//   Rect as SkiaRect,
//   vec,
// } from '@shopify/react-native-skia';
// import React, {ReactElement, memo, useMemo, useState} from 'react';
// import {
//   PanResponder,
//   Pressable,
//   Text as RNText,
//   StyleProp,
//   StyleSheet,
//   View,
//   ViewStyle,
//   useWindowDimensions,
// } from 'react-native';

// export type TimeRange = '24H' | '1W' | '1M' | '1Y' | 'ALL';
// export type PriceDataPoint = {
//   timestamp: number;
//   price: number;
// };

// export interface PriceChartProps {
//   data: PriceDataPoint[];
//   timeRange: TimeRange;
//   onTimeRangeChange: (range: TimeRange) => void;
//   symbol: string;
//   currentPrice: number;
//   priceChange: number;
//   priceChangePercentage: number;
//   highPrice: number;
//   lowPrice: number;
//   rewardsAPR?: string;
//   style?: StyleProp<ViewStyle>;
// }

// const TIME_RANGES: TimeRange[] = ['24H', '1W', '1M', '1Y', 'ALL'];
// const CHART_HEIGHT = 200;
// const PADDING = 20;

// const TimeRangeButton = React.memo(
//   ({
//     range,
//     isActive,
//     onPress,
//   }: {
//     range: TimeRange;
//     isActive: boolean;
//     onPress: () => void;
//   }): ReactElement => (
//     <Pressable
//       style={[styles.timeRangeButton, isActive && styles.timeRangeButtonActive]}
//       onPress={onPress}
//       accessibilityRole="button"
//       accessibilityState={{selected: isActive}}
//       accessibilityLabel={`${range} time range`}
//     >
//       <RNText style={[styles.timeRangeText, isActive && styles.timeRangeTextActive]}>
//         {range}
//       </RNText>
//     </Pressable>
//   ),
// );

// const formatPrice = (price: number): string =>
//   new Intl.NumberFormat('en-US', {
//     style: 'currency',
//     currency: 'USD',
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2,
//   }).format(price);

// const formatDate = (timestamp: number): string => {
//   const date = new Date(timestamp);
//   const hours = date.getHours();
//   const minutes = date.getMinutes();
//   const ampm = hours >= 12 ? 'PM' : 'AM';
//   const formattedHours = hours % 12 || 12;
//   const formattedMinutes = minutes.toString().padStart(2, '0');

//   const today = new Date();
//   const yesterday = new Date(today);
//   yesterday.setDate(yesterday.getDate() - 1);

//   if (date.toDateString() === today.toDateString()) {
//     return `Today ${formattedHours}:${formattedMinutes} ${ampm}`;
//   } else if (date.toDateString() === yesterday.toDateString()) {
//     return `Yesterday ${formattedHours}:${formattedMinutes} ${ampm}`;
//   } else {
//     return date.toLocaleDateString('en-US', {
//       month: 'short',
//       day: 'numeric',
//       hour: 'numeric',
//       minute: '2-digit',
//     });
//   }
// };

// const ChartSkeleton = React.memo(() => (
//   <View style={styles.skeletonContainer}>
//     <View style={styles.skeletonSymbol} />
//     <View style={styles.skeletonPrice} />
//     <View style={styles.skeletonChart} />
//     <View style={styles.skeletonButtons} />
//   </View>
// ));

// export const PriceChart = ({
//   data,
//   timeRange,
//   onTimeRangeChange,
//   symbol,
//   currentPrice,
//   priceChange,
//   priceChangePercentage,
//   highPrice,
//   lowPrice,
//   style,
// }: PriceChartProps): ReactElement => {
//   const {width: windowWidth} = useWindowDimensions();
//   const chartWidth = windowWidth;
//   const [touchX, setTouchX] = useState<number | null>(null);
//   const [touchPrice, setTouchPrice] = useState<number | null>(null);
//   const [touchDate, setTouchDate] = useState<number | null>(null);

//   const {linePath, areaPath, pricePoints, minPrice, maxPrice} = useMemo(() => {
//     if (data.length === 0) {
//       return {
//         linePath: '',
//         areaPath: '',
//         pricePoints: [],
//         minPrice: 0,
//         maxPrice: 0,
//       };
//     }

//     const prices = data.map((d) => d.price);
//     const min = Math.min(...prices);
//     const max = Math.max(...prices);
//     const priceRange = max - min;

//     const commands: string[] = [];
//     const points: {x: number; y: number; price: number}[] = [];

//     data.forEach((point, i) => {
//       const x = (i * chartWidth) / (data.length - 1);
//       const y = CHART_HEIGHT - ((point.price - min) / priceRange) * CHART_HEIGHT;

//       points.push({x, y, price: point.price});

//       if (i === 0) {
//         commands.push(`M ${x} ${y}`);
//       } else {
//         commands.push(`L ${x} ${y}`);
//       }
//     });

//     const pathString = commands.join(' ');
//     const areaPathString = `${pathString} L ${chartWidth} ${CHART_HEIGHT} L 0 ${CHART_HEIGHT} Z`;

//     return {
//       linePath: pathString,
//       areaPath: areaPathString,
//       pricePoints: points,
//       minPrice: min,
//       maxPrice: max,
//     };
//   }, [data, chartWidth]);

//   const panResponder = useMemo(
//     () =>
//       PanResponder.create({
//         onStartShouldSetPanResponder: () => true,
//         onMoveShouldSetPanResponder: () => true,
//         onPanResponderMove: (evt) => {
//           const x = evt.nativeEvent.locationX;
//           if (x < 0 || x > chartWidth) return;

//           setTouchX(x);
//           const closestPoint = pricePoints.reduce((prev, curr) => {
//             return Math.abs(curr.x - x) < Math.abs(prev.x - x) ? curr : prev;
//           });
//           setTouchPrice(closestPoint.price);

//           const index = Math.round((x / chartWidth) * (data.length - 1));
//           setTouchDate(data[index]?.timestamp || null);
//         },
//         onPanResponderRelease: () => {
//           setTouchX(null);
//           setTouchPrice(null);
//           setTouchDate(null);
//         },
//       }),
//     [chartWidth, pricePoints, data],
//   );

//   const isPositiveChange = priceChange >= 0;
//   const gradientColors = ['#fad3ac', '#ffffff'];
//   const lineColor = '#f7ba7d';
//   const pastLineColor = '#D1D5DB';

//   if (!data.length) {
//     return <ChartSkeleton />;
//   }

//   return (
//     <View style={[styles.container, style]}>
//       <View style={styles.contentContainer}>
//         <View style={styles.symbolContainer}>
//           <RNText style={styles.symbol}>{symbol}</RNText>
//         </View>

//         <View style={styles.priceContainer}>
//           <RNText style={styles.currentPrice}>
//             {touchPrice ? formatPrice(touchPrice) : formatPrice(currentPrice)}
//           </RNText>
//           <RNText style={[styles.priceChange, isPositiveChange && styles.positiveChange]}>
//             {isPositiveChange ? '+' : ''}
//             {priceChangePercentage.toFixed(2)}% ({formatPrice(Math.abs(priceChange))})
//           </RNText>
//           {touchDate && <RNText style={styles.dateText}>{formatDate(touchDate)}</RNText>}
//         </View>

//         <View style={styles.highLowContainer}>
//           <RNText style={styles.highLow}>↑ {formatPrice(highPrice)}</RNText>
//           <RNText style={styles.highLow}>↓ {formatPrice(lowPrice)}</RNText>
//         </View>
//       </View>

//       <View style={styles.chartWrapper}>
//         <View style={styles.chartContainer} {...panResponder.panHandlers}>
//           <Canvas style={{width: chartWidth, height: CHART_HEIGHT}}>
//             <Group>
//               {touchX !== null && (
//                 // Past data path (gray)
//                 <Path
//                   path={`${linePath.split('L')[0]} L ${touchX} ${
//                     CHART_HEIGHT -
//                     ((touchPrice! - minPrice) / (maxPrice - minPrice)) * CHART_HEIGHT
//                   }`}
//                   color={pastLineColor}
//                   style="stroke"
//                   strokeWidth={2}
//                 >
//                   <CornerPathEffect r={10} />
//                 </Path>
//               )}

//               <Path path={linePath} color={lineColor} style="stroke" strokeWidth={2}>
//                 <CornerPathEffect r={10} />
//               </Path>

//               <Mask
//                 mode="luminance"
//                 mask={
//                   <Path path={areaPath} color="white" style="fill">
//                     <CornerPathEffect r={8} />
//                   </Path>
//                 }
//               >
//                 <SkiaRect x={0} y={0} width={chartWidth} height={CHART_HEIGHT}>
//                   <LinearGradient
//                     start={vec(0, 0)}
//                     end={vec(0, CHART_HEIGHT)}
//                     colors={gradientColors}
//                   />
//                 </SkiaRect>
//               </Mask>

//               {touchX !== null && (
//                 <>
//                   <Line
//                     p1={vec(touchX, 0)}
//                     p2={vec(touchX, CHART_HEIGHT)}
//                     color="rgba(156, 163, 175, 0.5)"
//                     strokeWidth={1}
//                   />
//                   <Circle
//                     cx={touchX}
//                     cy={
//                       CHART_HEIGHT -
//                       ((touchPrice! - minPrice) / (maxPrice - minPrice)) * CHART_HEIGHT
//                     }
//                     r={6}
//                     color={lineColor}
//                   />
//                 </>
//               )}
//             </Group>
//           </Canvas>
//         </View>
//       </View>

//       <View style={styles.contentContainer}>
//         <View style={styles.timeRangeContainer}>
//           {TIME_RANGES.map((range) => (
//             <TimeRangeButton
//               key={range}
//               range={range}
//               isActive={timeRange === range}
//               onPress={() => onTimeRangeChange(range)}
//             />
//           ))}
//         </View>
//       </View>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     // backgroundColor: '#fff',
//   },
//   contentContainer: {
//     paddingHorizontal: 16,
//   },
//   symbolContainer: {
//     alignItems: 'center',
//     marginBottom: 8,
//   },
//   symbol: {
//     fontSize: 28,
//     fontWeight: '600',
//     color: '#000',
//   },
//   priceContainer: {
//     alignItems: 'center',
//     marginBottom: 8,
//   },
//   currentPrice: {
//     fontSize: 42,
//     fontWeight: '700',
//     color: '#000',
//     marginBottom: 4,
//     textAlign: 'center',
//   },
//   priceChange: {
//     fontSize: 18,
//     color: '#22c55e',
//     textAlign: 'center',
//   },
//   positiveChange: {
//     color: '#22c55e',
//   },
//   highLowContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//     gap: 16,
//     marginBottom: 16,
//   },
//   highLow: {
//     fontSize: 16,
//     color: '#6b7280',
//   },
//   chartWrapper: {
//     // marginHorizontal: -16,
//   },
//   chartContainer: {
//     height: CHART_HEIGHT,
//     marginBottom: 24,
//   },
//   timeRangeContainer: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     // backgroundColor: '#f3f4f6',
//     backgroundColor: '#fff',
//     borderRadius: 24,
//     padding: 4,
//   },
//   timeRangeButton: {
//     paddingVertical: 8,
//     paddingHorizontal: 16,
//     borderRadius: 20,
//   },
//   timeRangeButtonActive: {
//     backgroundColor: '#f3f4f6',
//     shadowColor: '#000',
//     shadowOffset: {width: 0, height: 1},
//     shadowOpacity: 0.1,
//     shadowRadius: 2,
//     elevation: 2,
//   },
//   timeRangeText: {
//     fontSize: 15,
//     color: '#6b7280',
//   },
//   timeRangeTextActive: {
//     color: '#000',
//     fontWeight: '600',
//   },
//   dateText: {
//     fontSize: 16,
//     color: '#6b7280',
//     textAlign: 'center',
//     marginTop: 4,
//   },
//   skeletonContainer: {
//     padding: 16,
//     // backgroundColor: '#fff',
//   },
//   skeletonSymbol: {
//     width: 120,
//     height: 24,
//     // backgroundColor: '#f3f4f6',
//     borderRadius: 4,
//     marginBottom: 16,
//   },
//   skeletonPrice: {
//     width: 180,
//     height: 32,
//     // backgroundColor: '#f3f4f6',
//     borderRadius: 4,
//     marginBottom: 50,
//   },
//   skeletonChart: {
//     width: '100%',
//     height: CHART_HEIGHT,
//     // backgroundColor: '#f3f4f6',
//     borderRadius: 4,
//     marginBottom: 20,
//   },
//   skeletonButtons: {
//     width: '100%',
//     height: 40,
//     // backgroundColor: '#f3f4f6',
//     borderRadius: 20,
//   },
// });

// export default memo(PriceChart);
