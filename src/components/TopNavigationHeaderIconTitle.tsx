import {memo} from 'react';
import {StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {MenuText} from '@/styles/styled-components';
import theme from '@/styles/theme';

const TopNavigationHeaderIconTitle = ({
  title,
  icon: Icon,
}: {
  title: string;
  icon?: React.ComponentType<any>;
}) => (
  <View style={styles.headerContainer}>
    {Icon && (
      <Icon width={theme.layout.images.xs - 8} height={theme.layout.images.xs - 8} />
    )}
    <MenuText style={styles.headerTitle}>{title}</MenuText>
  </View>
);

export default memo(TopNavigationHeaderIconTitle);

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 26,
  },
  headerTitle: {
    fontSize: 26,
    color: GlobalStyles.base.black,
    marginLeft: 8,
  },
});
