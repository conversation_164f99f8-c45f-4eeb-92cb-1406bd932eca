import NetInfo from '@react-native-community/netinfo';
import React, {useEffect, useState} from 'react';
import {StatusBar} from 'react-native';
import {OneSignal} from 'react-native-onesignal';
import {SafeAreaProvider} from 'react-native-safe-area-context';

import {ONESIGNAL_APP_ID} from '@env';
import FlashMessage from 'react-native-flash-message';
import {NavigationProvider} from './navigation/providers/NavigationProvider';

export default function App() {
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(!!state.isConnected);
    });

    OneSignal.initialize(ONESIGNAL_APP_ID);
    OneSignal.Notifications.requestPermission(true);

    return () => unsubscribe();
  }, []);

  return (
    <SafeAreaProvider>
      <StatusBar backgroundColor="#000" barStyle="dark-content" />

      <NavigationProvider isConnected={isConnected} />

      <FlashMessage position="top" />
    </SafeAreaProvider>
  );
}
