import {showMessage} from 'react-native-flash-message';

import GlobalStyles from '@/constants/GlobalStyles';

export const showInfoToast = (message: string) => {
  showMessage({
    floating: true,
    backgroundColor: GlobalStyles.gray.gray700,
    message,
  });
};

export const showSuccessToast = (message: string) => {
  showMessage({
    type: 'success',
    message,
  });
};

export const showWarningToast = (message: string) => {
  showMessage({
    floating: true,
    type: 'warning',
    message,
  });
};
