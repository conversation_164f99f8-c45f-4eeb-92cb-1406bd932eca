import Clipboard from '@react-native-clipboard/clipboard';
import {Share, Vibration} from 'react-native';
import RNFS from 'react-native-fs';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import {OneSignal} from 'react-native-onesignal';

import {NotificationInstance} from '../services/BackendServices';
import {showInfoToast} from './toast';

export const isProduction = (): boolean => process.env.NODE_ENV === 'production';

export const handleCopy = (text: string) => {
  if (!text) return;

  Clipboard.setString(text);
  showInfoToast(`Address copied\n${text}`);
};

export const handleSharePress = async (message: string) => {
  try {
    await Share.share({message});
  } catch (e: any) {
    console.error('Share error:', e); // Silent handling
  }
};

export const deleteDirectory = async (directoryPath: string) => {
  try {
    const exists = await RNFS.exists(directoryPath);
    if (exists) {
      await RNFS.unlink(directoryPath);
      console.log(`Directory deleted successfully -- ${directoryPath}`);
    } else {
      const eMessage = `Directory doesn't exist -- ${directoryPath}`;
      console.error('Error deleting directory:', eMessage);
      throw eMessage;
    }
  } catch (e: any) {
    console.error('Error deleting directory:', e);
    throw e;
  }
};

export const enableNotifications = async (btcAddress: string) => {
  const id = await OneSignal.User.pushSubscription.getIdAsync();

  if (!id) {
    return;
  }

  await NotificationInstance.post(`/device-data`, {
    btcAddress: btcAddress,
    deviceToken: id,
  });
};

export const shuffleArray = (array: any[]) => {
  if (array.length == 0) return;
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }

  return array;
};

export type TVibrate =
  | 'impactLight'
  | 'impactMedium'
  | 'impactHeavy'
  | 'notificationSuccess'
  | 'notificationWarning'
  | 'notificationError'
  | 'selection'
  | 'default';

/**
 * @param {TVibrate} type
 * @param {number} [pattern]
 */
export const vibrate = ({
  type = 'impactHeavy',
  pattern = 1000,
}: {
  type?: TVibrate;
  pattern?: number;
} = {}): void => {
  try {
    if (type === 'default') {
      Vibration.vibrate(pattern);
      return;
    }

    const options = {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    };
    ReactNativeHapticFeedback.trigger(type, options);
  } catch (e) {
    console.log(e);
  }
};

// TODO: Remove it
export const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1);
};

/**
 * Converts the entire string to uppercase.
 * @param {string} text
 */
export const capitalizeAll = (text: string): string => {
  return text.toUpperCase();
};

type FormatNumberOptions = {
  prefix?: string;
  suffix?: string;
  decimals?: number;
};

export const formatNumber = (
  value: string | number,
  options: FormatNumberOptions = {},
): string => {
  // "." is used in the Exchange rate to indicate that the rate is not available
  if (!value || value === '.') {
    return '0';
  }

  const {prefix = '', suffix = '', decimals = 8} = options;

  const formatter = Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
    useGrouping: true,
  });

  const formatted = formatter.format(Number(value));
  return `${prefix}${formatted}${suffix}`;
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
};
