import BottomTabBar from '@/components/BottomTabBar';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React, {memo, ReactElement} from 'react';

import CalculatorScreen from '@/screens/CalculatorScreen/CalculatorScreen';
import MoreScreen from '@/screens/More/More';
import NewsScreen from '@/screens/NewsScreen/NewsScreen';
import {BottomTabParamList} from '../types';
import ExchangeNavigator from './ExchangeNavigator';
import LightningNavigator from './LightningNavigator';
// import LoanNavigator from './LoanNavigator';
import {useAppSelector} from '@/hooks/redux';
import OnboardingNavigator from './OnboardingNavigator';
import WalletNavigator from './WalletNavigator';

const Tab = createBottomTabNavigator<BottomTabParamList>();

const BottomTabsNavigator = (): ReactElement => {
  const {isLoggedIn} = useAppSelector((state: any) => state.auth);

  return (
    <Tab.Navigator
      initialRouteName={!isLoggedIn ? 'Onboarding' : 'Wallet'}
      screenOptions={{
        headerShown: false,
      }}
      tabBar={(props) => {
        const currentRoute = props.state?.routes[props.state.index];

        // Hide tab bar for Loan screen
        if (currentRoute?.name === 'Loan') {
          return null;
        }

        // Hide tab bar for Subscribe screen
        if (currentRoute?.name === 'Wallet' && currentRoute.state?.routes) {
          const walletRoute =
            currentRoute.state.routes[currentRoute.state.routes.length - 1];

          if (walletRoute?.name === 'Subscribe') {
            return null;
          }
        }

        return <BottomTabBar {...props} isLoggedIn={isLoggedIn} />;
      }}
    >
      {!isLoggedIn ? (
        <Tab.Group>
          <Tab.Screen name="Onboarding" component={OnboardingNavigator} />

          {/* <Tab.Screen name="TopAssets" component={TopAssetsScreen} /> */}

          <Tab.Screen name="Calculator" component={CalculatorScreen} />

          <Tab.Screen name="News" component={NewsScreen} />
        </Tab.Group>
      ) : (
        <Tab.Group>
          <Tab.Screen name="Wallet" component={WalletNavigator} />

          <Tab.Screen name="Lightning" component={LightningNavigator} />

          {/* <Tab.Screen name="Loan" component={LoanNavigator} /> */}

          {<Tab.Screen name="Exchange" component={ExchangeNavigator} />}

          <Tab.Screen name="More" component={MoreScreen} />
        </Tab.Group>
      )}
    </Tab.Navigator>
  );
};

export default memo(BottomTabsNavigator);
