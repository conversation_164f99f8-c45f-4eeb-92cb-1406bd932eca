import React, {memo} from 'react';

import LightningHistorySheet from './LightningHistorySheet';
import LightningSendSheet from './LightningSendSheet';
import ListSheet from './ListSheet';
import WalletSheet from './WalletSheet';
import KeychainBackupSheet from './KeychainBackupSheet';

const BottomSheets = (): JSX.Element => {
  return (
    <>
      <ListSheet />
      <WalletSheet />

      <LightningHistorySheet />
      <LightningSendSheet />
      <KeychainBackupSheet />
    </>
  );
};

export default memo(BottomSheets);
