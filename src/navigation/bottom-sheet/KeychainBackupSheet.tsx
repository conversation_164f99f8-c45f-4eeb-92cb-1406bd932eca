import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';
import {CloudIcon} from 'react-native-heroicons/outline';

import BottomSheetScreen from '@/components/BottomSheetModalLayout';
import BottomSheetWrapper from '@/components/BottomSheetWrapper';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet, useSnapPoints} from '@/hooks/bottomSheet';
import {useAppSelector} from '@/hooks/redux';
import {KeychainStackParamList} from '@/navigation/types';
import {KEYCHAIN_BACKUP_PROVIDER} from '@/screens/KeychainBackup/helpers-keychain';
import {selectSheet} from '@/storage/slices/ui';

type KeychainHomeNavigationProp = StackNavigationProp<
  KeychainStackParamList,
  'KeychainHome'
>;

const KeychainBackupSheet = () => {
  const navigation = useNavigation<KeychainHomeNavigationProp>();
  const {isOpen, close} = useBottomSheet('keychainBackup');
  const sheet = useAppSelector((state) => selectSheet(state, 'keychainBackup'));

  const snapPointsToUse = Array.isArray(sheet.snapPoints)
    ? useSnapPoints(sheet.snapPoints[0]!, sheet.snapPoints[1]!)
    : useSnapPoints(sheet.snapPoints ?? 72);

  const handleContinue = () => {
    navigation.navigate('KeychainCreatePassword');
    close();
  };

  const CloudImage = (
    <View style={styles.iconContainer}>
      <CloudIcon size={48} color={GlobalStyles.primary.primary500} />
    </View>
  );

  if (!isOpen) return null;

  return (
    <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPointsToUse} onClose={close}>
      <BottomSheetScreen
        title={`Back up seed phrase to ${KEYCHAIN_BACKUP_PROVIDER}?`}
        description="Setting a password will encrypt your recovery phrase backup, adding an extra level of protection if your account is ever compromised."
        image={CloudImage}
        continueText="Continue"
        cancelText="Cancel"
        onContinue={handleContinue}
        onCancel={close}
      />
    </BottomSheetWrapper>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    backgroundColor: GlobalStyles.primary.primary100,
    borderRadius: 16,
    padding: 16,
    alignSelf: 'center',
    marginBottom: 16,
  },
});

export default memo(KeychainBackupSheet);
