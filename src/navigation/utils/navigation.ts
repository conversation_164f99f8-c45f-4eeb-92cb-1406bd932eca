import type {LinkingOptions} from '@react-navigation/native';
import {createNavigationContainerRef} from '@react-navigation/native';
import {Linking} from 'react-native';
import {OneSignal} from 'react-native-onesignal';
import {BottomTabParamList, RootStackParamList} from '../types';

export const navigationRef = createNavigationContainerRef<RootStackParamList>();

export function isNavigationReady(): boolean {
  return navigationRef.current?.isReady() ?? false;
}

export const navigate = <RouteName extends keyof RootStackParamList>(
  name: RouteName,
  params?: RootStackParamList[RouteName],
) => {
  if (!isNavigationReady()) return;

  if (navigationRef.current) {
    navigationRef.current.navigate({
      name,
      params,
    } as never);
  }
};

export const navigateViaBottomTabs = (
  stack: keyof BottomTabParamList,
  screen: string,
  params?: any,
) => {
  if (!isNavigationReady()) return;

  navigate('BottomTabs', {
    screen: stack as any,
    params: {
      screen: screen as any,
      params,
    },
  });
};

export const goBack = (): boolean => {
  if (!isNavigationReady()) return false;

  if (navigationRef.current?.canGoBack()) {
    navigationRef.current?.goBack();
    return true;
  }
  return false;
};

export const backToHome = () => {
  if (!isNavigationReady()) return;

  navigationRef.current?.navigate('BottomTabs', {
    screen: 'Wallet',
    params: {
      screen: 'WalletHome',
    },
  });
};

export const reset = (routeName: keyof RootStackParamList) => {
  if (!isNavigationReady()) return;
  navigationRef.current?.reset({
    index: 0,
    routes: [{name: routeName}],
  });
};

export const resetToHome = () => {
  if (!isNavigationReady()) return;

  navigationRef.current?.reset({
    index: 0,
    routes: [{name: 'BottomTabs', params: {screen: 'Wallet', params: {screen: 'Home'}}}],
  });
};

/* ============================================================================================== */
/*                                       DEEP LINK HANDLING                                       */
/* ============================================================================================== */

/**
 * Deep linking configuration. Handles both deep links and notification clicks.
 */
export const linking: LinkingOptions<RootStackParamList> = {
  prefixes: ['assetify://'],
  config: {
    screens: {
      BottomTabs: {
        screens: {
          Wallet: {
            screens: {
              Home: {
                path: 'bottom-navigation/:dummy',
                parse: {
                  dummy: (dummy: string) => {
                    return dummy;
                  },
                  asset: (asset: string) => {
                    return asset;
                  },
                },
              },
            },
          },
          Lightning: 'bottom-navigation/Breez',
        },
      },
    },
  },
  getInitialURL: async () => {
    const url = await Promise.race([
      Linking.getInitialURL(),
      new Promise<string | null>((resolve) => {
        OneSignal.Notifications.addEventListener('click', (event) => {
          if (event.result.url) {
            resolve(event.result.url);
          } else {
            resolve(null);
          }
        });
      }),
    ]);
    return url;
  },
  subscribe: (listener) => {
    const linkingSubscription = Linking.addEventListener('url', ({url}) => {
      if (!url) {
        console.log('No URL in deep link');
        return;
      }
      listener(url);
    });

    return () => {
      linkingSubscription.remove();
    };
  },
};
