import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {RefreshControl, ScrollView, View} from 'react-native';
import {connect} from 'react-redux';
import FixedText from '../../../../components/FixedText/FixedText';
import GlobalStyles from '../../../../constants/GlobalStyles';
import {NotificationInstance} from '../../../../services/BackendServices';
import NotificationComponent from './components/Notification';
import {Notification} from './notificationsUtils';
import {styles} from './styles';

type NotificationsProps = {
  userAddresses: AuthAddresses;
};

const Notifcations: React.FC = (props: any) => {
  const viewFocused = useIsFocused();
  const {t} = useTranslation();
  const {userAddresses} = props as NotificationsProps;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const fetch = async () => {
    if (viewFocused) {
      const res = await NotificationInstance.get(
        // @ts-ignore
        `/wallet/notifications/${userAddresses[0].address}`,
      );

      setNotifications(res.data);
    }
  };

  useEffect(() => {
    fetch();
  }, [viewFocused]);

  const onRefresh = () => {
    setRefreshing(true);
    fetch();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  return (
    <>
      <ScrollView
        style={GlobalStyles.THEME.mainContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={GlobalStyles.primary.primary500}
            colors={[GlobalStyles.primary.primary500]}
          />
        }
      >
        <View style={styles.notificationsContainer}>
          <View style={styles.container}>
            <View style={styles.listContainer}>
              {notifications && notifications?.length !== 0 ? (
                notifications.map((item: Notification, index: number) => {
                  return (
                    <NotificationComponent
                      key={index}
                      index={index}
                      notification={item}
                    />
                  );
                })
              ) : (
                <View style={styles.noAssetContainer}>
                  <FixedText style={styles.tokenName}>
                    Your notifications will appear here
                  </FixedText>
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>
    </>
  );
};

const mapStateToProps = (state: any) => ({
  userAddresses: state.auth.userAddress ?? state.auth.userAddresses,
});

export default connect(mapStateToProps, null)(Notifcations);
