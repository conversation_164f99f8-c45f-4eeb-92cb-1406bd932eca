import React, {memo, useCallback, useState} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';

import LightningColoredIcon from '@/assets/logo/lightning-colored.svg';
import Banner from '@/components/Banner';
import BottomSheetScreen from '@/components/BottomSheetScreen';
import CurrencyInput from '@/components/CurrencyInput';
import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import SwitchWithLabel from '@/components/Switch';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useScreenSize} from '@/hooks/screen';
import QrForm from './components/QrForm';
import {useLightningReceive} from './hooks/useLightningReceive';

const LightningReceive = (): JSX.Element => {
  const {isSmallScreen} = useScreenSize();

  const [isSheetOpen, setIsSheetOpen] = useState(true);

  const {
    state: {
      url,
      amountInSat,
      note,
      openingFeeInSat,
      isSwitched,
      amountInUsd,
      isLoading,
      feeMessage,
    },
    actions,
    isQrCodeLoading,
    qrCode,
  } = useLightningReceive();

  const bottomSheetHeight = isSmallScreen ? 70 : 60;

  const renderQrSection = useCallback(
    () => (
      <View style={styles.qrSection}>
        <QrForm
          qrCode={qrCode}
          url={url}
          isLoading={isLoading || isQrCodeLoading}
          openingFeeInSat={openingFeeInSat}
        />

        <View style={styles.bannerContainer}>
          <Banner type="warning" message={feeMessage} />
        </View>

        <Text style={styles.generateTitle}>Other Methods</Text>
      </View>
    ),
    [isQrCodeLoading, isLoading, qrCode, url, openingFeeInSat, feeMessage, actions],
  );

  const renderInvoiceSection = useCallback(
    () => (
      <View style={styles.generateInvoiceContainer}>
        <Text style={styles.sectionTitle}>Generate Invoice</Text>

        <View style={styles.inputContainer}>
          <CurrencyInput
            label="You Receive"
            selectedOption={{fullName: 'Satoshis', label: 'SATS', type: 'crypto'}}
            value={amountInSat}
            onChangeText={actions.handleOnChangeAmount}
            onBlur={actions.handleAmountOnBlur}
            decimals={0}
            onOptionPress={() => {}}
            optionsDisabled={true}
            styles={{
              container: {
                marginBottom: 6,
              },
            }}
          />
          <Text style={styles.approximateValue}>
            {`Approximate value: ${amountInUsd} USD`}
          </Text>
        </View>

        <SwitchWithLabel
          disabled={false}
          label="Include Note"
          isEnabled={isSwitched}
          handleSwitch={actions.handleSwitch}
          restContainerStyles={styles.switchContainer}
        />

        {isSwitched && (
          <View style={styles.noteContainer}>
            <TextInput
              label="Your Note"
              placeholder="Enter your note..."
              value={note}
              onChangeText={actions.setNote}
              onBlur={actions.handleAmountOnBlur}
            />
          </View>
        )}
      </View>
    ),
    [amountInUsd, isSwitched, note, actions],
  );

  return (
    <KeyboardAvoidingView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderQrSection()}

        {renderInvoiceSection()}
      </ScrollView>

      <BottomSheetScreen
        isOpen={isSheetOpen}
        onClose={() => setIsSheetOpen(false)}
        icon={<LightningColoredIcon width={150} height={150} />}
        title="About Payments"
        description="Please keep the app open and active until your transaction has been completed."
        height={bottomSheetHeight}
      />
    </KeyboardAvoidingView>
  );
};

export default memo(LightningReceive);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  qrSection: {
    marginTop: 32,
  },
  bannerContainer: {
    marginTop: 22,
    paddingHorizontal: 10,
  },
  generateTitle: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    fontSize: 18,
    marginTop: 32,
    marginBottom: 18,
    marginLeft: 4,
  },
  generateInvoiceContainer: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.primary.primary900,
    marginBottom: 16,
    fontFamily: GlobalStyles.fonts.poppins,
  },
  inputContainer: {
    marginBottom: 16,
  },
  approximateValue: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.gray.gray700,
    fontSize: 14,
    marginLeft: 4,
  },
  switchContainer: {
    marginLeft: 2,
    marginBottom: 16,
  },
  noteContainer: {
    marginTop: 16,
  },
});
