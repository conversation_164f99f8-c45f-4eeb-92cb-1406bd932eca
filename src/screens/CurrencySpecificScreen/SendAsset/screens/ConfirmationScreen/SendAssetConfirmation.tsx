import {useIsFocused, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import WebView from 'react-native-webview';
import {connect} from 'react-redux';

import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import {useAppDispatch} from '@/hooks/redux';
import Success from '@/screens/Success';
import {
  AuthAddress,
  AuthAddresses,
  AuthAddressType,
  AuthAsset,
  AuthUser,
} from '@/types/authTypes';
import {triggerBiometrics} from '@/utils/biometrics';
import Check from '../../../../../assets/icons/check.svg';
import FixedText from '../../../../../components/FixedText/FixedText';
import GlobalStyles from '../../../../../constants/GlobalStyles';
import {navigate} from '../../../../../navigation/utils/navigation';
import {WalletBalanceInstance} from '../../../../../services/BackendServices';
import WalletService from '../../../../../services/WalletService';
import {findWalletIndex} from '../../../currencySpecificUtils';
import {debugging} from '../../sendAssetUtils';
import {
  getPreparedTx,
  sendAndBroadcastSolanaTransaction,
} from '../SendAssetInput/SendAssetInputUtils';
import {styles} from '../SendAssetInput/styles';

const CheckSvg = () => <Check width={52} height={52} />;

type SendAssetConfirmationProps = {
  user: AuthUser;
  userAddresses: AuthAddresses;
};

type SendAssetConfirmationParams = {
  builtTx: any;
  recipientAddress: string;
  amount: string;
  stableCoin: string;
  asset: AuthAsset;
  assets: AuthAsset[] | null;
  calculatedAmount: string;
  calculatedFee: string;
  tronFee: string;
  approximateFee: string;
  address: AuthAddressType | null;
  calculatedPrices: string[][];
  tokenPrices: string[][];
  stableCoinIndex: number;
  stableCoinAmount: string;
  solanaBlockhash: string;
  solanaPDAWallet: string[];
};

const SendAssetConfirmation: React.FC = (props: any) => {
  const route = useRoute();
  const {
    builtTx,
    recipientAddress,
    amount,
    stableCoin,
    asset,
    assets,
    calculatedAmount,
    calculatedFee,
    tronFee,
    approximateFee,
    address,
    calculatedPrices,
    tokenPrices,
    stableCoinIndex,
    stableCoinAmount,
    solanaBlockhash,
    solanaPDAWallet,
  } = route.params as SendAssetConfirmationParams;

  const {t} = useTranslation();
  const viewFocused = useIsFocused();
  const dispatch = useAppDispatch();

  const {user, userAddresses} = props as SendAssetConfirmationProps;

  const [disabled, setDisabled] = useState(false);
  const [signedKAS, setSignedKAS] = useState(null);
  const [payload, setPayload] = useState(debugging);
  const [success, setSuccess] = useState(false);

  const confirmationLogoContainer = StyleSheet.flatten([
    styles.logoContainer,
    {shadowColor: GlobalStyles.success.success600},
  ]);

  const squeezeAddress = (address: string) => {
    return address.substring(0, 6) + '...' + address.substring(38, 42);
  };

  const onMessage = (payload: any) => {
    let dataPayload;

    try {
      dataPayload = JSON.parse(payload.nativeEvent.data);
    } catch (e) {
      console.log(e);
    }

    console.log(dataPayload);

    if (dataPayload.type === 'Console') {
      if (dataPayload.data.type === 'info') {
        const log = JSON.parse(dataPayload.data.log);
        setSignedKAS(log);
      }
    }
  };

  useEffect(() => {
    if (signedKAS !== null) {
      WalletBalanceInstance.post(`/broadcast-kaspa`, {
        //@ts-ignore
        transaction: signedKAS.tx,
      })
        .then((res) => {
          console.log('res', res);
          setDisabled(false);
          setSuccess(true);
        })
        .catch((error) => {
          console.log('error', error.response);
          setDisabled(false);
        });
    }
  }, [signedKAS]);

  const handleConfirmPress = async () => {
    setDisabled(true);

    if (asset?.tokenSymbol === 'SOL') {
      const result = await triggerBiometrics();

      if (result.success) {
        await sendAndBroadcastSolanaTransaction(
          address,
          builtTx,
          stableCoin,
          asset,
          userAddresses,
          recipientAddress,
          amount,
          solanaPDAWallet,
        );

        setSuccess(true);
        return;
      }
    }

    // const ownerAddress = address?.address as Address;

    // const tokenProgramAddress = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address;
    // const ataProgramId = 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' as Address;

    // console.log('asset', asset);

    // if (asset?.tokenSymbol === 'SOL') {
    //   const response = await fetch(
    //     'https://dev.assetify.net/balance-fetcher/transaction/solana/mainnet/v2/getfee',
    //     {
    //       method: 'POST',
    //       headers: {
    //         'Content-Type': 'application/json',
    //       },
    //       body: builtTx,
    //     },
    //   );

    //   console.log('response', response);

    //   if (!response.ok) {
    //     throw new Error('Failed to fetch fee information');
    //   }

    //   const feeData = await response.json();
    //   const freshSolanaBlockhash = feeData.solana.blockhash;

    //   console.log('freshSolanaBlockhash', freshSolanaBlockhash);

    //   // Solana Token logic
    //   if (stableCoin) {
    //     const result = await triggerBiometrics();

    //     if (result.success) {
    //       const tokenMintAddress = getSolanaMintAddress(stableCoin);

    //       const addressEncoder = getAddressEncoder();

    //       // Derive sender's associated token account (ATA)
    //       const pdaSender = await getProgramDerivedAddress({
    //         programAddress: ataProgramId,
    //         seeds: [
    //           addressEncoder.encode(ownerAddress as Address), // sender wallet address
    //           addressEncoder.encode(tokenProgramAddress), // token mint address
    //           addressEncoder.encode(tokenMintAddress as Address),
    //         ],
    //       });
    //       console.log('pdaSender', pdaSender);

    //       const isOnCurve = SolanaWeb3PublicKey.isOnCurve(recipientAddress);

    //       const pdaRecipient = await getProgramDerivedAddress({
    //         programAddress: ataProgramId,
    //         seeds: [
    //           addressEncoder.encode(
    //             // Wallet Address
    //             recipientAddress as Address,
    //           ),
    //           addressEncoder.encode(
    //             // Mint Address
    //             tokenProgramAddress,
    //           ),
    //           // Token Program Address
    //           addressEncoder.encode(tokenMintAddress as Address),
    //         ],
    //       });

    //       console.log('pdaRecipient', pdaRecipient);
    //       console.log('pdaSender', pdaSender);
    //       console.log('amount', amount);

    //       console.log('userAddresses[2].privateKey', userAddresses[2].privateKey);

    //       const senderTokenAddress = pdaSender[0]; // e.g., "Cyqexb2zv2kA2Wo3ws6Ne5B6wUyQE2oWmhp7fvcK2iDe"
    //       const recipientTokenAddress = pdaRecipient[0]; // e.g., "Feoe1mim72UXjs7dCNQuDGhDEhUX7kkckQxNzRYLunkR"

    //       console.log('sender and recipient usdc addresses');
    //       console.log(senderTokenAddress);
    //       console.log(recipientAddress);

    //       // Other parameters remain the same
    //       const fromAddress =
    //         typeof address === 'string'
    //           ? (address as Address)
    //           : (address?.address as Address); // wallet address (sender)
    //       const toAddress = recipientAddress; // recipient wallet address
    //       const value = parseFloat(amount) * 1e6; // use your amount value
    //       const tokenMint = tokenMintAddress;
    //       const decimals = 6;
    //       const blockhash = freshSolanaBlockhash;

    //       const privateKeyHex = userAddresses[2].privateKey?.toString();

    //       console.log('includes', solanaPDAWallet.includes(senderTokenAddress));

    //       const tx = await WalletManagerBridge.signSolanaTokenTransaction(
    //         fromAddress,
    //         toAddress,
    //         senderTokenAddress,
    //         isOnCurve ? recipientTokenAddress : recipientAddress,
    //         value,
    //         tokenMint,
    //         decimals,
    //         blockhash,
    //         privateKeyHex,
    //         isOnCurve ? !solanaPDAWallet.includes(recipientTokenAddress) : false,
    //       );

    //       console.log('tx', tx);

    //       const response = await fetch(
    //         'https://dev.assetify.net/balance-fetcher/broadcast-transaction/solana',
    //         {
    //           method: 'POST',
    //           headers: {
    //             'Content-Type': 'application/json',
    //           },
    //           body: JSON.stringify({tx: tx}),
    //         },
    //       );

    //       if (!response.ok) {
    //         console.log('error', response);
    //         setDisabled(false);
    //         return;
    //       }

    //       setSuccess(true);

    //       return;
    //     }
    //   }

    //   // Solana Coin logic
    //   if (!stableCoin) {
    //     const result = await triggerBiometrics();

    //     console.log('SOL >>>>>');
    //     console.log('address', typeof address === 'string' ? address : address?.address);
    //     console.log('recipientAddress', recipientAddress);
    //     console.log('amount', amount);
    //     console.log('amount in lamports', parseFloat(amount) * 1e9);
    //     console.log('userAddresses[0].privateKey', userAddresses[2].privateKey);

    //     const addressString = typeof address === 'string' ? address : address?.address;
    //     const amountInLamports = parseFloat(amount) * 1e9;

    //     if (result.success) {
    //       const tx = await WalletManagerBridge.signSolanaTransaction(
    //         addressString,
    //         recipientAddress,
    //         amountInLamports,
    //         freshSolanaBlockhash,
    //         userAddresses[2].privateKey,
    //       );

    //       console.log('tx', JSON.stringify({tx: tx}));

    //       await new Promise<void>((resolve) => {
    //         setTimeout(() => {
    //           resolve();
    //         }, 1000);
    //       });

    //       const response = await fetch(
    //         'https://dev.assetify.net/balance-fetcher/broadcast-transaction/solana',
    //         {
    //           method: 'POST',
    //           headers: {
    //             'Content-Type': 'application/json',
    //           },
    //           body: JSON.stringify({tx: tx}),
    //         },
    //       );

    //       if (!response.ok) {
    //         console.log('error', response);
    //         setDisabled(false);
    //         return;
    //       }

    //       console.log('response', response);

    //       setSuccess(true);
    //       return;
    //     }
    //   }
    // }

    const result = await triggerBiometrics();

    if (result.success) {
      const {tx, wallet, privKey} = await getPreparedTx(
        stableCoin,
        asset,
        user.wallet,
        recipientAddress,
        Number(amount),
        userAddresses,
      );

      if (tx && asset.blockchain !== 'kaspa') {
        await new WalletService().sendPreparedTx(tx, privKey, wallet);
      } else if (tx && asset.blockchain === 'kaspa') {
        const privKeys = userAddresses[
          // @ts-ignore
          findWalletIndex(asset.tokenSymbol)
        ].addresses.map((address: AuthAddress) => address.privateKey);
        setPayload(
          debugging +
            `\nmultipleHashTx(${JSON.stringify(tx)}, ${JSON.stringify(
              privKeys,
            )}).then((res) => {console.info(res)});`,
        );

        return;
      }

      setSuccess(true);
    }
  };

  useEffect(() => {
    setDisabled(false);
    setSuccess(false);
    setPayload(debugging);
    setSignedKAS(null);
  }, [viewFocused]);

  return (
    <>
      {!success ? (
        <>
          <View style={GlobalStyles.THEME.mainContainer}>
            <View style={styles.container}>
              <View style={confirmationLogoContainer}>
                <CheckSvg />
              </View>
              <View style={styles.infoContainer}>
                <View style={styles.receivingContainer}>
                  <FixedText style={styles.leftText}>
                    {t('currencySpecific.receivingAddr')}
                  </FixedText>
                  <FixedText style={styles.rightText}>
                    {squeezeAddress(recipientAddress)}
                  </FixedText>
                </View>
              </View>
              <View style={styles.line} />
              <View style={styles.infoContainer}>
                <View style={styles.receivingContainer}>
                  <FixedText style={styles.leftText}>Amount</FixedText>
                  <View
                    style={{
                      ...styles.rightContainer,
                      ...{flexDirection: 'row'},
                    }}
                  >
                    <FixedText style={styles.rightText}>
                      {amount} {stableCoin != '' ? stableCoin : asset?.tokenSymbol}
                    </FixedText>
                  </View>
                </View>
              </View>
              <View style={styles.line} />
              <View style={styles.infoContainer}>
                <View
                  style={{
                    ...styles.receivingContainer,
                    ...{height: 50, marginBottom: 0},
                  }}
                >
                  <View style={{width: '20%'}}>
                    <FixedText style={styles.leftText}>Fee</FixedText>
                  </View>
                  <View style={styles.rightContainer}>
                    <FixedText style={styles.rightText}>
                      {calculatedFee}{' '}
                      {asset?.tokenSymbol === 'TRX' ? 'Bandwidth' : asset?.tokenSymbol}
                      {asset?.tokenSymbol === 'TRX' && tronFee !== '0'
                        ? ` | ${tronFee} TRX`
                        : ''}
                    </FixedText>
                    <FixedText style={styles.approximateFee}>
                      {approximateFee !== '' && approximateFee !== 'NaN'
                        ? `≈ ${approximateFee} USD`
                        : null}
                    </FixedText>
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.bottomContainer}>
            <HoldToConfirmButton onPress={handleConfirmPress} disabled={disabled} />
          </View>

          <View style={{height: 0, width: 0, paddingBottom: 20}}>
            {payload != debugging && (
              <WebView
                ref={
                  // @ts-ignore
                  (ref) => (this.webview = ref)
                }
                source={{
                  uri: 'https://dev.assetify.net/kaspa-worker/index.html',
                }}
                javaScriptEnabled={true}
                onLoad={() => {
                  // @ts-ignore
                  this.webview.injectJavaScript(payload);
                }}
                onMessage={onMessage}
              />
            )}
          </View>
        </>
      ) : (
        <Success
          isSuccess={success}
          title={t('currencySpecific.txSent')}
          description={t('currencySpecific.txSentDescription')}
          onFinish={() => {
            navigate('BottomTabs', {
              screen: 'Wallet',
              params: {
                screen: 'WalletHome',
              },
            });
          }}
        />
      )}
    </>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.auth.user,
  userAddresses: state.auth.userAddress ?? state.auth.userAddresses,
});

export default connect(mapStateToProps)(SendAssetConfirmation);
