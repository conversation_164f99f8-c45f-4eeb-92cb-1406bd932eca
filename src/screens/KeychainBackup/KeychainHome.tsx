import React, {memo, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, SafeAreaView, StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {showInfoToast, showWarningToast} from '@/utils/toast';
import MButton from '@components/MButton';
import {BodyM, Caption, Footer} from '@styles/styled-components';
import theme from '@styles/theme';
import {KEYCHAIN_BACKUP_PROVIDER, deleteKeychainWalletService} from './helpers-keychain';

const KeychainHome = ({navigation, route}: {navigation: any; route: any}) => {
  const {walletLabel, encryptedData} = route.params;

  const {t} = useTranslation();

  const confirmDeleteBackup = useCallback(() => {
    Alert.alert(
      t('keychain.deleteBackup', {opt: KEYCHAIN_BACKUP_PROVIDER}),
      t('keychain.deleteBackupDescription', {opt: KEYCHAIN_BACKUP_PROVIDER}),
      [
        {
          text: t('cancel'),
          style: 'cancel',
        },
        {
          text: t('delete'),
          onPress: handleDeleteBackup,
          style: 'destructive',
        },
      ],
      {cancelable: true},
    );
  }, []);

  const handleDeleteBackup = useCallback(async () => {
    try {
      const success = await deleteKeychainWalletService(walletLabel, encryptedData);

      if (success) {
        showInfoToast('Backup deleted');
        navigation.navigate('Settings', {screen: 'SettingsHome'});
      } else {
        console.error('[KeychainHome] Error deleting backup');
        showWarningToast('Error deleting backup');
        navigation.navigate('Settings', {screen: 'SettingsHome'});
      }
    } catch (error) {
      console.error('[KeychainHome] Delete error:', error);
    }
  }, [encryptedData, navigation, walletLabel]);

  return (
    <SafeAreaView style={styles.root}>
      <View style={styles.content}>
        <Caption style={styles.title}>
          {t('keychain.keychainBackup', {opt: KEYCHAIN_BACKUP_PROVIDER})}
        </Caption>

        <BodyM style={styles.description}>
          {t('keychain.keychainHomeDescription', {opt: KEYCHAIN_BACKUP_PROVIDER})}
        </BodyM>

        <View style={styles.statusContainer}>
          <BodyM style={styles.statusLabel}>Seed phrase</BodyM>

          <BodyM style={[styles.statusValue, styles.backedUp]}>Backed up ✓</BodyM>
        </View>
      </View>

      <Footer>
        <MButton text="Delete Backup" onPress={confirmDeleteBackup} variant="secondary" />
      </Footer>
    </SafeAreaView>
  );
};

export default memo(KeychainHome);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: theme.layout.ph.screen + 12,
    paddingVertical: theme.layout.pv.screen * 2,
    gap: theme.spacing.xl,
  },
  title: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    color: GlobalStyles.gray.gray900,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    backgroundColor: GlobalStyles.gray.gray200,
    borderRadius: theme.layout.borderRadius.md,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
  },
  statusLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  backedUp: {
    color: GlobalStyles.success.success700,
  },
});
