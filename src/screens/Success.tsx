import React, {useEffect, useRef} from 'react';
import {Animated, StyleSheet, View} from 'react-native';

import CheckIcon from '@/assets/icons/check.svg';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useNavigation} from '@react-navigation/native';

const NUM_PARTICLES = 8;

type SuccessProps = {
  title: string;
  description: string;
  onFinish: () => void;
  isSuccess: boolean;
  buttonText?: string;
};

const Success = ({
  title,
  description,
  onFinish,
  buttonText = 'Finish',
  isSuccess,
}: SuccessProps) => {
  const navigation = useNavigation();
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const particles = useRef(
    [...Array(NUM_PARTICLES)].map(() => ({
      scale: new Animated.Value(0),
      translateX: new Animated.Value(0),
      translateY: new Animated.Value(0),
      opacity: new Animated.Value(1),
    })),
  ).current;

  useEffect(() => {
    if (isSuccess) {
      navigation.setOptions({
        gestureEnabled: false,
        headerShown: false,
      });
    } else {
      navigation.setOptions({
        gestureEnabled: true,
        headerShown: true,
      });
    }
  }, [isSuccess, navigation]);

  useEffect(() => {
    // Main check icon animation
    const checkAnimation = Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
      tension: 40,
      friction: 7,
    });

    const fadeIn = Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    });

    // Particle animations
    const particleAnimations = particles.map((particle, index) => {
      const angle = (index * 2 * Math.PI) / NUM_PARTICLES;
      const distance = 100;

      return Animated.parallel([
        Animated.sequence([
          Animated.spring(particle.scale, {
            toValue: 1,
            tension: 80,
            friction: 5,
            useNativeDriver: true,
          }),
          Animated.timing(particle.scale, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.spring(particle.translateX, {
            toValue: Math.cos(angle) * distance,
            tension: 80,
            friction: 5,
            useNativeDriver: true,
          }),
          Animated.timing(particle.translateX, {
            toValue: Math.cos(angle) * (distance + 50),
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.spring(particle.translateY, {
            toValue: Math.sin(angle) * distance,
            tension: 80,
            friction: 5,
            useNativeDriver: true,
          }),
          Animated.timing(particle.translateY, {
            toValue: Math.sin(angle) * (distance + 50),
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(particle.opacity, {
            toValue: 0.6,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(particle.opacity, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ]),
      ]);
    });

    const animations = [checkAnimation, fadeIn];
    animations.push(...particleAnimations);

    Animated.parallel(animations).start();
  }, []);

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.iconWrapper}>
          {particles.map((particle, index) => (
            <Animated.View
              key={index}
              style={[
                styles.particle,
                {
                  transform: [
                    {scale: particle.scale},
                    {translateX: particle.translateX},
                    {translateY: particle.translateY},
                  ],
                  opacity: particle.opacity,
                },
              ]}
            />
          ))}

          <Animated.View
            style={[
              styles.iconContainer,
              {
                transform: [{scale: scaleAnim}],
                opacity: opacityAnim,
              },
            ]}
          >
            <CheckIcon width={140} height={140} />
          </Animated.View>
        </View>

        <Animated.Text style={[styles.title, {opacity: opacityAnim}]}>
          {title}
        </Animated.Text>

        <Animated.Text style={[styles.description, {opacity: opacityAnim}]}>
          {description}
        </Animated.Text>
      </View>

      <View style={styles.footer}>
        <MButton text={buttonText} onPress={onFinish} />
      </View>
    </View>
  );
};

export default Success;

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 24,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconWrapper: {
    position: 'relative',
    width: 240,
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
  },
  particle: {
    position: 'absolute',
    width: 8,
    height: 8,
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  title: {
    color: GlobalStyles.base.black,
    fontSize: 26,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    color: GlobalStyles.gray.gray800,
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 24,
    lineHeight: 24,
  },
  footer: {
    width: '95%',
    alignSelf: 'center',
  },
});
