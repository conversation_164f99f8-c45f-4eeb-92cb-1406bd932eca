import {useFocusEffect} from '@react-navigation/native';
import React, {memo, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import {WalletIcon} from 'react-native-heroicons/outline';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {setUser} from '@/storage/actions/authActions';
import {Caption} from '@/styles/styled-components';
import {AuthUserWallet} from '@/types/authTypes';
import {showWarningToast} from '@/utils/toast';
import theme from '@styles/theme';
const {Enumerations, Services} = require('@AssetifyNet/cryptoapis-kms');

const NewWalletCreating = ({navigation}: any) => {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();

  const createNewWallet = async () => {
    await new Promise((resolve) => setTimeout(resolve, 2_000));

    try {
      const walletService = new Services.WalletService(
        Enumerations.Blockchains.BITCOIN,
        'mainnet',
      );

      const wallet = await walletService.createHDWallet();

      const formattedWallet: AuthUserWallet = {
        blockchain: Enumerations.Blockchains.BITCOIN,
        network: 'mainnet',
        mnemonic: wallet.mnemonic,
        seed: wallet.seed,
        zPub: wallet.xPubsList[0],
      };

      dispatch(setUser({wallet: [formattedWallet], pinCode: ''}));

      navigation.replace('NewWallet');
    } catch (e) {
      console.log('[CreatingNewWalletScreen] Error: ', e);
      showWarningToast('Something went wrong. Please try again');
      navigation.goBack();
    }
  };

  useFocusEffect(
    useCallback(() => {
      createNewWallet();
    }, []),
  );

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <WalletIcon size={theme.layout.images.xs} stroke={GlobalStyles.gray.gray800} />
        </View>

        <Caption>{t('wallet.creatingWallet')}</Caption>

        <View style={styles.seedPhraseContainer}>
          <SkeletonPlaceholder speed={1_500}>
            <SkeletonPlaceholder.Item
              flexDirection="row"
              flexWrap="wrap"
              justifyContent="space-between"
              width="100%"
            >
              {Array.from({length: 12}).map((_, index) => (
                <SkeletonPlaceholder.Item
                  key={index}
                  width="30%"
                  height={40}
                  marginBottom={theme.spacing.md}
                  borderRadius={theme.layout.borderRadius.sm}
                />
              ))}
            </SkeletonPlaceholder.Item>
          </SkeletonPlaceholder>
        </View>
      </View>
    </View>
  );
};

export default memo(NewWalletCreating);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.md,
    paddingVertical: theme.layout.pv.screen,
    alignItems: 'center',
    gap: theme.spacing.xxxl,
  },
  iconContainer: {
    width: 58,
    height: 58,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.layout.borderRadius.sm,
  },
  seedPhraseContainer: {
    width: '100%',
    paddingHorizontal: theme.spacing.sm,
  },
  seedWordWrapper: {
    width: '30%',
    height: 40,
    padding: theme.spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray500,
    borderRadius: theme.layout.borderRadius.sm,
  },
});
