import {useFocusEffect} from '@react-navigation/native';
import React, {memo, useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {vibrate} from '@/utils';
import MButton from '@components/MButton';
import Switch from '@components/Switch';
import {BodyM, Footer, Title} from '@styles/styled-components';
import theme from '@styles/theme';

const AgreePopUpDialog: React.FC<{
  onAction: () => void;
  switches: Array<any>;
}> = ({onAction, switches}) => {
  const {t} = useTranslation();

  const [checkboxStates, setCheckboxStates] = useState(switches);

  const toggleCheckbox = useCallback(
    (index: number) => {
      const updatedCheckboxes = [...checkboxStates];
      updatedCheckboxes[index].checked = !updatedCheckboxes[index].checked;
      setCheckboxStates(updatedCheckboxes);
      vibrate();
    },
    [checkboxStates],
  );

  const handleButtonPress = useCallback(() => {
    onAction();
    setCheckboxStates(switches);
  }, [onAction, switches]);

  useFocusEffect(
    useCallback(() => {
      const updatedCheckboxes = [...checkboxStates];
      updatedCheckboxes.forEach((checkbox: any) => {
        checkbox.checked = false;
      });
      setCheckboxStates(updatedCheckboxes);
    }, []),
  );

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <Title style={styles.captionText}>{t('wallet.important_notice')}</Title>
          <BodyM style={styles.subtitleText}>{t('wallet.tap_checkboxes')}</BodyM>
        </View>

        <View style={styles.switchesContainer}>
          {checkboxStates.map((checkbox, index) => (
            <Switch
              key={index}
              label={t(checkbox.text)}
              handleSwitch={() => toggleCheckbox(index)}
              isEnabled={checkbox.checked}
            />
          ))}
        </View>
      </View>

      <Footer style={styles.footer}>
        <MButton
          text={t('continue')}
          onPress={handleButtonPress}
          disabled={!checkboxStates.every((item) => item.checked)}
        />
      </Footer>
    </View>
  );
};

export default memo(AgreePopUpDialog);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    paddingHorizontal: theme.layout.ph.sm + 8,
    paddingTop: theme.layout.pv.md,
    gap: theme.spacing.lg,
  },
  titleContainer: {
    paddingHorizontal: theme.layout.ph.sm,
    gap: theme.spacing.lg,
  },
  captionText: {
    textAlign: 'center',
  },
  subtitleText: {
    color: GlobalStyles.gray.gray900,
  },
  switchesContainer: {
    flexDirection: 'column',
    paddingVertical: theme.spacing.md,
    backgroundColor: GlobalStyles.gray.gray300,
    borderRadius: theme.layout.borderRadius.lg,
  },
  footer: {
    paddingHorizontal: theme.layout.ph.md,
  },
});
