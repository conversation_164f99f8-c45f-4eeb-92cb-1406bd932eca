import {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {ChevronRightIcon, KeyIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {BodyM, Caption} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {stripServicePrefix} from '../../helpers/import-wallet-helpers';

type RecoveryPhraseItem = {
  service: string;
  displayName: string;
};

const RestoreWalletListAvailable = ({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) => {
  const {loading, recoveryPhrases} = route.params;

  const {t} = useTranslation();

  const handlePhraseSelect = (item: RecoveryPhraseItem) => {
    navigation.navigate('RestoreRecoveryAndImport', {
      recoveryId: stripServicePrefix(item.service),
    });
  };

  const renderItem = ({item}: {item: RecoveryPhraseItem}) => {
    return (
      <TouchableOpacity
        style={styles.itemContainer}
        onPress={() => handlePhraseSelect(item)}
        activeOpacity={0.7}
      >
        <View style={styles.iconContainer}>
          <KeyIcon size={28} stroke={GlobalStyles.gray.gray900} />
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.title}>{item.displayName}</Text>
        </View>

        <ChevronRightIcon size={24} stroke={theme.colors.base.black} />
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Caption style={styles.loadingText}>{t('wallet.authenticate')}</Caption>
      </View>
    );
  }

  if (recoveryPhrases.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <KeyIcon
          stroke={theme.colors.base.black}
          size={48}
          style={styles.emptyContainerIcon}
        />

        <Caption style={styles.header}>{t('wallet.noRecoveryPhrases')}</Caption>

        <BodyM style={styles.subheader}>{t('wallet.noRecoveryPhrasesDescription')}</BodyM>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Caption style={styles.header}>{t('wallet.selectBackupToRestore')}</Caption>

      <BodyM style={styles.subheader}>
        {recoveryPhrases.length > 1
          ? t('wallet.multipleRecoveryPhrases')
          : t('wallet.oneRecoveryPhrase')}
      </BodyM>

      <FlatList
        data={recoveryPhrases}
        renderItem={renderItem}
        keyExtractor={(item) => item.service}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default memo(RestoreWalletListAvailable);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.light.background,
    paddingHorizontal: theme.layout.ph.sm,
    paddingTop: 40,
  },
  header: {
    textAlign: 'center',
    marginBottom: 16,
  },
  subheader: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
  },
  listContent: {
    paddingBottom: 20,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray500,
    marginBottom: 16,
    backgroundColor: theme.colors.light.background,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F4F4F4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  icon: {
    fontSize: 24,
  },
  contentContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  id: {
    fontSize: 14,
    color: theme.colors.light.secondary,
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.light.background,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 20,
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.light.background,
    paddingHorizontal: theme.layout.ph.md,
    gap: theme.spacing.md,
  },
  emptyContainerIcon: {
    marginBottom: theme.spacing.sm,
  },
});
