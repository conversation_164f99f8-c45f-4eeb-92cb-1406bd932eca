import Clipboard from '@react-native-clipboard/clipboard';
import analytics from '@react-native-firebase/analytics';
import {useIsFocused} from '@react-navigation/native';
import {wordlists} from 'bip39';
import React, {forwardRef, ReactElement, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

import CopyPasteButton from '@/components/CopyPasteBtn';
import GlobalStyles from '@/constants/GlobalStyles';
import seedSuggestions from '@/screens/Onboarding/helpers';
import theme from '@/styles/theme';
import {showWarningToast} from '@/utils/toast';
import {getFontSize, getScreenAlignment} from '@/utils/parsing';

type TInputMnemonic = {
  handleMnemonics: (text: string) => void;
  handleMnemonicsSubmit: (text: string) => void;
  mnemonics: string;
};

type TWordProps = {
  title: string;
  onPress: () => void;
};

const Word: React.FC<TWordProps> = ({title, onPress}): ReactElement => (
  <TouchableOpacity onPress={onPress} style={styles.wordContainer}>
    <Text style={styles.wordText}>{title}</Text>
  </TouchableOpacity>
);

const InputMnemonic = forwardRef<TextInput, TInputMnemonic>(
  ({handleMnemonics, handleMnemonicsSubmit, mnemonics}, ref) => {
    const [input, setInput] = useState(mnemonics);
    const [word, setWord] = useState('');
    const [suggestions, setSuggestions] = useState<string[]>([]);

    const viewFocused = useIsFocused();
    const inputAccessoryViewID = 'suggestionsView';
    const {t} = useTranslation();

    useEffect(() => {
      setWord(input.split(' ').pop() || '');
    }, [input]);

    useEffect(() => {
      setSuggestions(word ? seedSuggestions(word, wordlists.english!) : []);
    }, [word]);

    useEffect(() => {
      if (!viewFocused) setInput('');
    }, [viewFocused]);

    useEffect(() => {
      const words = input.trim().split(/\s+/);
      if (words.length === 12 && words[11] !== '') {
        handleMnemonicsSubmit(input.trim());
      }
      if (words.length > 12) {
        showWarningToast(t('settings.incorrectSeedPhrase'));
      }
    }, [input, handleMnemonicsSubmit]);

    const handleOnChangeText = (text: string) => {
      setInput(text);
      handleMnemonics(text);
    };

    const handleWordPress = (selectedWord: string) => {
      const newText = `${input.split(' ').slice(0, -1).concat(selectedWord).join(' ')} `;
      setInput(newText);
      handleMnemonics(newText);
    };

    const pasteFromClipboard = async () => {
      analytics().logEvent('ImportWallet_pasteButton_tap');
      const text = await Clipboard.getString();
      setInput(text);
      handleMnemonicsSubmit(text);
    };

    const renderSuggestions = () => (
      <View style={styles.suggestionsContainer}>
        <View style={styles.suggestionRowContainer}>
          {suggestions.length ? (
            suggestions.map((s) => (
              <Word title={s} key={s} onPress={() => handleWordPress(s)} />
            ))
          ) : (
            <Text style={styles.placeholder}>{t('wallet.suggestionsAccessory')}</Text>
          )}
        </View>
      </View>
    );

    const renderAndroidSuggestions = () => {
      if (suggestions.length === 0) return null;
      return renderSuggestions();
    };

    return (
      <View style={styles.mainContainer}>
        <View style={styles.mnemonicInputContainer}>
          <TextInput
            ref={ref}
            style={styles.mnemonicInput}
            multiline
            onChangeText={handleOnChangeText}
            value={input}
            autoCorrect={false}
            spellCheck={false}
            inputAccessoryViewID={
              Platform.OS === 'ios' ? inputAccessoryViewID : undefined
            }
            maxLength={120}
            placeholder="Enter your recovery phrase"
            placeholderTextColor={GlobalStyles.gray.gray700}
          />
        </View>
        <View style={styles.pasteBtnContainer}>
          <CopyPasteButton clipboardAction={pasteFromClipboard} title={t('paste')} />
        </View>

        {/* {Platform.OS === 'ios' && (
        <InputAccessoryView nativeID={inputAccessoryViewID}>
          {renderSuggestions()}
        </InputAccessoryView>
      )} */}
      </View>
    );
  },
);

export default InputMnemonic;

const styles = StyleSheet.create({
  mainContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    alignSelf: 'center',
    padding: theme.spacing.md,
    position: 'relative',
  },
  title: {
    color: GlobalStyles.primary.primary500,
    marginBottom: theme.spacing.sm,
  },
  mnemonicInputContainer: {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    marginBottom: theme.spacing.lg,
    width: '100%',
    marginRight: 'auto',
    height: theme.isSmallDevice ? 100 : 120,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
  },
  mnemonicInput: {
    width: '100%',
    height: '100%',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: getFontSize(18),
    color: GlobalStyles.base.black,
    padding: theme.spacing.xl,
    flexShrink: 1,
    textAlignVertical: 'top',
  },
  suggestionsContainer: {
    width: theme.SCREEN_WIDTH,
    backgroundColor: GlobalStyles.gray.gray100,
    paddingVertical: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: GlobalStyles.gray.gray500,
  },
  suggestionRowContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexShrink: 1,
    flexWrap: 'wrap',
  },
  wordContainer: {
    marginRight: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
  },
  wordText: {
    color: GlobalStyles.primary.primary500,
    fontSize: theme.isSmallDevice ? 14 : 16,
    fontWeight: '500',
  },
  placeholder: {
    color: GlobalStyles.gray.gray800,
    fontSize: theme.isSmallDevice ? 14 : 16,
    fontWeight: '400',
  },
  pasteBtnContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: +getScreenAlignment(theme.SCREEN_HEIGHT, '23', '13'),
  },
});
