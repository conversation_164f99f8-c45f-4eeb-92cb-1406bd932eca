import {QueryClientProvider} from '@tanstack/react-query';
import i18next from 'i18next';
import React, {ReactElement} from 'react';
import {initReactI18next} from 'react-i18next';
import {ActivityIndicator} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {configureReanimatedLogger, ReanimatedLogLevel} from 'react-native-reanimated';
import {enableFreeze, enableScreens} from 'react-native-screens';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';

import {languageResources} from '@/constants/i18next';
import {persistor, store} from '@/storage/store';
import App from './App';
import ErrorBoundary from './ErrorBoundary';
import {queryClient} from './utils/queries';

enableScreens(true);
enableFreeze(true);

const Root = (): ReactElement => {
  return (
    <Provider store={store}>
      <ErrorBoundary>
        <GestureHandlerRootView>
          <PersistGate loading={<ActivityIndicator />} persistor={persistor}>
            <QueryClientProvider client={queryClient}>
              <App />
            </QueryClientProvider>
          </PersistGate>
        </GestureHandlerRootView>
      </ErrorBoundary>
    </Provider>
  );
};

i18next.use(initReactI18next).init({
  compatibilityJSON: 'v3',
  lng: 'en',
  fallbackLng: 'en',
  resources: languageResources,
});

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
});

export default Root;
