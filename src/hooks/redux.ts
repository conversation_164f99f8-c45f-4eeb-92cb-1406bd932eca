import {createSelector} from '@reduxjs/toolkit';
import {TypedUseSelectorHook, useDispatch, useSelector} from 'react-redux';
import type {AppDispatch, RootState} from '../storage/store';

// Use throughout the app instead of plain useDispatch and useSelector
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Create typed hooks for each reducer
export const useAuth = () => {
  return useSelector(
    createSelector(
      (state: RootState) => state.auth,
      (auth) => ({
        user: auth.user,
        // @ts-ignore - ensures correct production state
        userAddresses: auth.userAddress ?? auth.userAddresses,
        isLoggedIn: auth.isLoggedIn,
        calculatedPrices: auth.calculatedPrices,
        tokenPrices: auth.tokenPrices,
        assets: auth.assets,
        stableCoins: auth.stableCoins,
        walletBalance: auth.walletBalance,
        avalancheFixed: auth.avalancheFixed,
        walletTxs: auth.walletTxs,
        isAuthenticated: auth.isAuthenticated,
        showAuthScreen: auth.showAuthScreen,
        biometricsInProgress: auth.biometricsInProgress,
      }),
    ),
  );
};

export const useCommon = () => {
  return useSelector(
    createSelector(
      (state: RootState) => state.common,
      (common) => ({
        language: common.language,
        currency: common.currency,
        onboardingSavedToICloud: common.onboardingSavedToICloud,
        walletIdentifier: common.walletIdentifier,
        seedPhraseVerified: common.seedPhraseVerified,
      }),
    ),
  );
};

export const useLightning = () => {
  return useSelector(
    createSelector(
      (state: RootState) => state.lightning,
      (lightning) => ({
        workingDir: lightning.workingDir,
        modalCount: lightning.modalCount,
        hideModalPermanently: lightning.hideModalPermanently,
        webSocket: lightning.webSocket,
        balanceMsat: lightning.balanceMsat,
        refundablesLength: lightning.refundablesLength,
      }),
    ),
  );
};

export const useExchange = () => {
  return useSelector(
    createSelector(
      (state: RootState) => state.exchange,
      (exchange) => ({
        payinAmount: exchange.payinAmount,
        payoutAmount: exchange.payoutAmount,
        rate: exchange.rate,
        rateID: exchange.rateID,
        timeLeft: exchange.timeLeft,
        isPaused: exchange.isPaused,
      }),
    ),
  );
};

export const useUI = () => {
  return useSelector(
    createSelector(
      (state: RootState) => state.ui,
      (ui) => ({
        list: ui.list,
        wallet: ui.wallet,
      }),
    ),
  );
};
