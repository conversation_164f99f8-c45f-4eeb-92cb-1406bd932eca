const INITIAL_STATE = {
  language: 'en',
  currency: 'USD',
  seedPhraseConfirmed: 6,
  onboardingSavedToICloud: false,
  walletIdentifier: null,
  seedPhraseVerified: false,
};

export default (state = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case 'CHANGE_LANGUAGE':
      return {
        ...state,
        language: action.payload,
      };
    case 'CHANGE_CURRENCY':
      return {
        ...state,
        currency: action.payload,
      };
    case 'SET_SEED_PHRASE_CONFIRMED':
      return {
        ...state,
        seedPhraseConfirmed: action.payload,
      };
    case 'SET_ONBOARDING_SAVED_TO_ICLOUD':
      return {
        ...state,
        onboardingSavedToICloud: action.payload,
      };
    case 'SET_WALLET_IDENTIFIER':
      return {
        ...state,
        walletIdentifier: action.payload,
      };
    case 'SET_SEED_PHRASE_VERIFIED':
      return {
        ...state,
        seedPhraseVerified: action.payload,
      };
    default:
      return state;
  }
};
