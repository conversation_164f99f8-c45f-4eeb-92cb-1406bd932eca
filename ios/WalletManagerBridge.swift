import Foundation
import WalletCore
import React

@objc(WalletManagerBridge)
class WalletManagerBridge: NSObject, RCTBridgeModule {
  static func moduleName() -> String! {
    return "WalletManagerBridge"
  }
  
  static func requiresMainQueueSetup() -> Bool {
    return true
  }

  // Existing wallet creation method...
  @objc(createHDWalletFromMnemonic:blockchain:resolver:rejecter:)
  func createHDWalletFromMnemonic(_ mnemonic: String, blockchain: String, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    let walletManager = WalletManager()
    let walletDictionary = walletManager.createHDWalletFromMnemonic(mnemonic: mnemonic, blockchain: blockchain)
    
    if walletDictionary.isEmpty {
      rejecter("Error", "Invalid mnemonic or unable to generate seed.", nil)
    } else {
      resolver(walletDictionary)
    }
  }
  
  // MARK: - Transaction Manager Methods
  
  // Option 1: Create a new instance for every transaction
  @objc(buildUnsignedSolanaTransaction:toAddress:lamports:resolver:rejecter:)
  func buildUnsignedSolanaTransaction(
    _ fromAddress: String,
    toAddress: String,
    lamports: NSNumber,
    resolver: @escaping RCTPromiseResolveBlock,
    rejecter: @escaping RCTPromiseRejectBlock
  ) {
      let transactionManager = TransactionManager()
      guard let unsignedTx = transactionManager.buildUnsignedSolanaTransaction(
        fromAddress: fromAddress,
        toAddress: toAddress,
        lamports: lamports
      ) else {
          rejecter("BUILD_UNSIGNED_ERROR", "Unable to build unsigned transaction", nil)
          return
      }
      resolver(unsignedTx)
  }
  
  @objc(signSolanaTransaction:toAddress:lamports:blockhash:privateKeyHex:resolver:rejecter:)
  func signSolanaTransaction(
    _ fromAddress: String,
    toAddress: String,
    lamports: NSNumber,
    blockhash: String,
    privateKeyHex: String,
    resolver: @escaping RCTPromiseResolveBlock,
    rejecter: @escaping RCTPromiseRejectBlock
  ) {
      let transactionManager = TransactionManager()
      guard let signedTx = transactionManager.signSolanaTransaction(
        fromAddress: fromAddress,
        toAddress: toAddress,
        lamports: lamports,
        blockhash: blockhash,
        privateKeyHex: privateKeyHex
      ) else {
          rejecter("SIGN_TX_ERROR", "Unable to sign transaction", nil)
          return
      }
      resolver(signedTx)
  }
  
  // New method: Sign a Solana token transfer transaction.
  @objc(signSolanaTokenTransaction:toAddress:senderTokenAddress:recipientTokenAddress:amount:tokenMint:decimals:blockhash:privateKeyHex:shouldCreateAssociatedTokenAccount:resolver:rejecter:)
  func signSolanaTokenTransaction(
    _ fromAddress: String,
    toAddress: String,
    senderTokenAddress: String,
    recipientTokenAddress: String,
    amount: NSNumber,
    tokenMint: String,
    decimals: NSNumber,
    blockhash: String,
    privateKeyHex: String,
    shouldCreateAssociatedTokenAccount: Bool,
    resolver: @escaping RCTPromiseResolveBlock,
    rejecter: @escaping RCTPromiseRejectBlock
  ) {
      let transactionManager = TransactionManager()
      guard let signedTx = transactionManager.signSolanaTokenTransaction(
        fromAddress: fromAddress,
        toAddress: toAddress,
        senderTokenAddress: senderTokenAddress,
        recipientTokenAddress: recipientTokenAddress,
        amount: amount,
        tokenMint: tokenMint,
        decimals: decimals,
        blockhash: blockhash,
        privateKeyHex: privateKeyHex,
        shouldCreateAssociatedTokenAccount: shouldCreateAssociatedTokenAccount
      ) else {
          rejecter("SIGN_TOKEN_TX_ERROR", "Unable to sign token transaction", nil)
          return
      }
      resolver(signedTx)
  }
}
