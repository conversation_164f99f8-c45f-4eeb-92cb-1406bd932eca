//
//  WalletManager.swift
//  AssetifyApp
//
//  Created by <PERSON> on 3.04.24.
//
import Foundation
import WalletCore

@objc(WalletDTO)
class WalletDTO: NSObject {
  @objc let blockchain: String
  @objc let network: String
  @objc let mnemonic: String
  @objc let seed: String
  @objc var xPubsList: [[String: String]]
  @objc let address: String
  @objc let privateKey: String
  @objc let publicKey: String

  init(blockchain: String, network: String, mnemonic: String, seed: Data,
           xPubsList: [[String: String]], address: String, privateKey: String, publicKey: String) {
          self.blockchain = blockchain
          self.network = network
          self.mnemonic = mnemonic
          self.seed = seed.map { String(format: "%02x", $0) }.joined()
          self.xPubsList = xPubsList
          self.address = address
          self.privateKey = privateKey
          self.publicKey = publicKey
          super.init()
      }

  func toDictionary() -> [String: Any] {
    return [
      "blockchain": self.blockchain,
      "network": self.network,
      "mnemonic": self.mnemonic,
      "seed": self.seed,
      "xPubsList": self.xPubsList,
      "address": self.address,
      "privateKey": self.privateKey,
      "publicKey": self.publicKey,
    ]
  }
}

class WalletManager: NSObject {

  func createHDWalletFromMnemonic(mnemonic: String, blockchain: String) -> [String: Any] {

    let hdWallet = HDWallet(mnemonic: mnemonic, passphrase: "")
    
    guard let seed = hdWallet?.seed else {
      return [:]
    }
    var xPubsList = [[String: String]]()
    var privateKey: PrivateKey
    var publicKey: PublicKey
    var address: String

    var coin: CoinType

    switch blockchain {
    case "ethereum":
      coin = .ethereum
    case "binance-smart-chain":
      coin = .ethereum
    case "trx":
      coin = .tron
    case "xrp":
      coin = .xrp
    case "litecoin":
      coin = .litecoin
    case "dogecoin":
      coin = .dogecoin
    case "bitcoin-cash":
      coin = .bitcoinCash
    case "avalanche":
      coin = .avalancheCChain
    case "solana":
      coin = .solana
    default:
      coin = .bitcoin
    }
    
    if blockchain == "kaspa" {
      privateKey = (hdWallet?.getKeyByCurve(curve: .secp256k1, derivationPath: "m/44'/111111'/0'/0/0"))!
      publicKey = privateKey.getPublicKeySecp256k1(compressed: true)
      
      address = ""
    } else if coin == .bitcoin || coin == .litecoin {
      // BIP84
      guard let xPriv = hdWallet?.getExtendedPrivateKey(purpose: .bip84, coin: coin, version: .zprv)
      else {
        return [:]
      }
      guard let xPub = hdWallet?.getExtendedPublicKey(purpose: .bip84, coin: coin, version: .zpub)
      else {
        return [:]
      }
      
      xPubsList.append([
        "accountXpub": xPub,
        "accountXpriv": xPriv,
      ])

      privateKey = (hdWallet?.getKeyDerivation(coin: coin, derivation: .default))!
      publicKey = privateKey.getPublicKey(coinType: coin)

      address = AnyAddress(publicKey: publicKey, coin: coin).description

    } else {
      // BIP44
      guard let xPriv = hdWallet?.getExtendedPrivateKey(purpose: .bip44, coin: coin, version: .xprv)
      else {
        return [:]
      }
      guard let xPub = hdWallet?.getExtendedPublicKey(purpose: .bip44, coin: coin, version: .xpub)
      else {
        return [:]
      }
      
      xPubsList.append([
        "accountXpub": xPub,
        "accountXpriv": xPriv,
      ])

      privateKey = (hdWallet?.getKeyDerivation(coin: coin, derivation: .default))!
      publicKey = privateKey.getPublicKey(coinType: coin)

      address = AnyAddress(publicKey: publicKey, coin: coin).description
    }

    let walletDTO = WalletDTO(
      blockchain: blockchain, network: "mainnet", mnemonic: mnemonic, seed: seed,
      xPubsList: xPubsList, address: address, privateKey: privateKey.data.toHexString(),
      publicKey: publicKey.description

    )

    return walletDTO.toDictionary()

  }
}

extension Data {
  func toHexString() -> String {
    return map { String(format: "%02hhx", $0) }.joined()
  }
}
